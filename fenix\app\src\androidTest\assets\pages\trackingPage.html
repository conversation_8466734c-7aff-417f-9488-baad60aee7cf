<!DOCTYPE HTML>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<!-- This asset is using the code behind
   - https://www.mozilla-anti-tracking.com/test/trackingprotection/test_pages/tracking_protection.html
   - test page.
   - Source repository: https://github.com/mozilla/anti-tracking-test-pages -->
<html dir="ltr" xml:lang="en-US" lang="en-US">
  <head>
    <meta charset="utf8">
    <script src="../resources/trackingAPI.js" type="text/javascript"></script>
  </head>
  <body>
    <h3>Level 1 (Basic) List</h3>
    <p>social-track-digest256:</p>
    <img
            src="https://social-track-digest256.dummytracker.org/test_not_blocked.png" alt="social not blocked"
            onerror="this.onerror=null;this.src='https://not-a-tracker.dummytracker.org/test_blocked.png';this.alt='social blocked'">
    <br/>
    <p>ads-track-digest256:</p>
    <img
            src="https://ads-track-digest256.dummytracker.org/test_not_blocked.png" alt="ads not blocked"
            onerror="this.onerror=null;this.src='https://not-a-tracker.dummytracker.org/test_blocked.png';this.alt='ads blocked'">
    <br/>
    <p>analytics-track-digest256:</p>
    <img
            src="https://analytics-track-digest256.dummytracker.org/test_not_blocked.png" alt="analytics not blocked"
            onerror="this.onerror=null;this.src='https://not-a-tracker.dummytracker.org/test_blocked.png';this.alt='analytics blocked'">
    <br/>
    <p>Fingerprinting:
    <pre id="result">test not run</pre>
    <script src="https://base-fingerprinting-track-digest256.dummytracker.org/tracker.js"
            onerror="this.onerror=null;var result=document.getElementById('result');result.innerHTML='Fingerprinting blocked';"
            onload="this.onload=null;var result=document.getElementById('result');result.innerHTML='Fingerprinting not blocked';"
    ></script>
    </p>
    <br/>
    <p>Cryptomining:
      <img
              src="https://base-cryptomining-track-digest256.dummytracker.org/test_not_blocked.png" alt="Cryptomining not blocked"
              onerror="this.onerror=null;this.src='https://not-a-tracker.dummytracker.org/test_blocked.png';this.alt='Cryptomining blocked'">
    </p>

    <p><b>Cookie blocking</b>
    </p>
    <iframe height=0 width=0 src="https://social-tracking-protection-facebook-digest256.dummytracker.org/cookie_access_test.html?test_origin=senglehardt.com"></iframe>
    <iframe height=0 width=0 src="https://social-tracking-protection-linkedin-digest256.dummytracker.org/cookie_access_test.html?test_origin=senglehardt.com"></iframe>
    <iframe height=0 width=0 src="https://social-tracking-protection-twitter-digest256.dummytracker.org/cookie_access_test.html?test_origin=senglehardt.com"></iframe>
    <p>
      * Facebook-cookies <pre id="social-tracking-protection-facebook-digest256"></pre>
    * LinkedIn-cookies <pre id="social-tracking-protection-linkedin-digest256"></pre>
    * Twitter-cookies <pre id="social-tracking-protection-twitter-digest256"></pre>
    </p>

    <script>
      function updateCookieStatus(statusMessage, list) {
          var output = document.getElementById(list);
          if (statusMessage === 'cookies') {
            output.innerHTML = "Cookies not blocked";
          } else if (statusMessage === 'no_cookies') {
            output.innerHTML = "Blocked";
          } else {
            output.innerHTML = "Unrecognized status";
          }
      }
      window.addEventListener("message", event => {
        lists = [
          'social-tracking-protection-facebook-digest256',
          'social-tracking-protection-linkedin-digest256',
          'social-tracking-protection-twitter-digest256'
        ];
        lists.forEach(list => {
          if (event.origin === `https://${list}.dummytracker.org`) {
            updateCookieStatus(event.data, list);
          }
        });
      }, false);
  </script>
  </body>
</html>
