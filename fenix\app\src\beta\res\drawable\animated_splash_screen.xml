<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt" >
    <aapt:attr name="android:drawable">
        <vector
            android:width="432dp"
            android:height="432dp"
            android:viewportWidth="432"
            android:viewportHeight="432">
            <group
                android:name="a1_t"
                android:pivotX="216"
                android:pivotY="216">
                <path
                    android:pathData="M303.8,251.4h-79.2c-2.3,0 -4.2,1.9 -4.2,4.2v12.7c0,18.7 15.1,33.8 33.8,33.8l0,0H298c14,0 25.3,-11.4 25.3,-25.3v-11.1C323.3,259.3 317,251.4 303.8,251.4L303.8,251.4z"
                    android:fillColor="#008787"/>
                <path
                    android:pathData="M303.8,251.4h-79.2c-2.3,0 -4.2,1.9 -4.2,4.2v12.7c0,18.7 15.1,33.8 33.8,33.8l0,0H298c14,0 25.3,-11.4 25.3,-25.3v-11.1C323.3,259.3 317,251.4 303.8,251.4L303.8,251.4z"
                    android:strokeAlpha="0.9"
                    android:fillAlpha="0.9">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="301.7"
                            android:startY="300.03"
                            android:endX="274.29"
                            android:endY="274.08"
                            android:type="linear">
                            <item android:offset="0" android:color="#7F054096"/>
                            <item android:offset="0.05" android:color="#700F3D9C"/>
                            <item android:offset="0.26" android:color="#3F2F35B1"/>
                            <item android:offset="0.47" android:color="#1C462FBF"/>
                            <item android:offset="0.67" android:color="#07542BC8"/>
                            <item android:offset="0.86" android:color="#00592ACB"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M316.4,179.2c-4.7,-11.3 -14.2,-23.5 -21.7,-27.4c6.1,11.9 9.6,23.9 11,32.9c0,0 0,0 0,0.2c-12.3,-30.5 -33,-42.8 -50,-69.7c-0.9,-1.4 -1.7,-2.7 -2.6,-4.1c-0.4,-0.7 -0.8,-1.5 -1.2,-2.3c-0.7,-1.4 -1.3,-2.8 -1.6,-4.3c0,-0.1 -0.1,-0.3 -0.2,-0.3c0,0 -0.1,0 -0.2,0l0,0c0,0 0,0 -0.1,0l0,0c-27.2,16 -36.5,45.4 -37.3,60.2c-10.9,0.8 -21.3,4.7 -29.8,11.5c-0.9,-0.8 -1.8,-1.4 -2.8,-2.1c-2.5,-8.7 -2.6,-17.8 -0.3,-26.5c-11.1,5 -19.8,13.1 -26.1,20.2l0,0c-4.3,-5.5 -4,-23.4 -3.7,-27.1c0,-0.2 -3.2,1.6 -3.6,1.9c-3.8,2.7 -7.3,5.7 -10.6,9.1c-3.7,3.7 -7.1,7.8 -10.1,12.1l0,0l0,0c-7,9.9 -11.9,21 -14.5,32.8c0,0.2 -0.1,0.5 -0.1,0.7c-0.2,0.9 -0.9,5.7 -1.1,6.8c0,0.1 0,0.1 0,0.2c-0.9,4.9 -1.5,9.9 -1.8,15c0,0.2 0,0.4 0,0.5c0,59.7 48.5,108.2 108.3,108.2c53.6,0 98.1,-38.9 106.8,-90c0.2,-1.4 0.3,-2.8 0.5,-4.2C325.6,215 323.2,195.5 316.4,179.2zM191.5,263.9c0.5,0.2 1,0.5 1.5,0.7c0,0 0,0 0.1,0C192.6,264.5 192.1,264.2 191.5,263.9zM305.7,184.8L305.7,184.8L305.7,184.8L305.7,184.8z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="296.59"
                            android:startY="138.83"
                            android:endX="128.16"
                            android:endY="312.38"
                            android:type="linear">
                            <item android:offset="0.05" android:color="#FFFFF44F"/>
                            <item android:offset="0.37" android:color="#FFFF980E"/>
                            <item android:offset="0.53" android:color="#FFFF3647"/>
                            <item android:offset="0.7" android:color="#FFE31587"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M316.4,179.2c-4.7,-11.3 -14.2,-23.5 -21.7,-27.4c6.1,11.9 9.6,23.9 11,32.9c0,0 0,0 0,0.1v0.1c10.2,27.7 4.6,55.9 -3.4,73.1c-12.4,26.6 -42.5,54 -89.5,52.6c-50.8,-1.4 -95.5,-39.2 -103.9,-88.6c-1.5,-7.8 0,-11.8 0.8,-18.1c-0.9,4.9 -1.3,6.3 -1.8,15c0,0.2 0,0.4 0,0.5c0,59.8 48.5,108.3 108.3,108.3c53.6,0 98.1,-38.9 106.8,-90c0.2,-1.4 0.3,-2.8 0.5,-4.2C325.6,215 323.2,195.5 316.4,179.2z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="297.63"
                            android:centerY="151.73"
                            android:gradientRadius="221.61"
                            android:type="radial">
                            <item android:offset="0.13" android:color="#FFFFBD4F"/>
                            <item android:offset="0.28" android:color="#FFFF980E"/>
                            <item android:offset="0.47" android:color="#FFFF3750"/>
                            <item android:offset="0.78" android:color="#FFEB0878"/>
                            <item android:offset="0.86" android:color="#FFE50080"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M316.4,179.2c-4.7,-11.3 -14.2,-23.5 -21.7,-27.4c6.1,11.9 9.6,23.9 11,32.9c0,0 0,0 0,0.1v0.1c10.2,27.7 4.6,55.9 -3.4,73.1c-12.4,26.6 -42.5,54 -89.5,52.6c-50.8,-1.4 -95.5,-39.2 -103.9,-88.6c-1.5,-7.8 0,-11.8 0.8,-18.1c-0.9,4.9 -1.3,6.3 -1.8,15c0,0.2 0,0.4 0,0.5c0,59.8 48.5,108.3 108.3,108.3c53.6,0 98.1,-38.9 106.8,-90c0.2,-1.4 0.3,-2.8 0.5,-4.2C325.6,215 323.2,195.5 316.4,179.2z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="213.81"
                            android:centerY="222.16"
                            android:gradientRadius="227.15"
                            android:type="radial">
                            <item android:offset="0.3" android:color="#FF960E18"/>
                            <item android:offset="0.35" android:color="#BCB11927"/>
                            <item android:offset="0.43" android:color="#56DB293D"/>
                            <item android:offset="0.5" android:color="#16F5334B"/>
                            <item android:offset="0.53" android:color="#00FF3750"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M263.7,191.9c0.2,0.2 0.5,0.3 0.7,0.5c-2.7,-4.8 -6.1,-9.2 -10.1,-13.1c-33.7,-33.7 -8.8,-73 -4.6,-75l0,0c-27.2,16 -36.5,45.4 -37.3,60.2c1.3,-0.1 2.5,-0.2 3.8,-0.2C236.6,164.2 254.3,175.4 263.7,191.9L263.7,191.9z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="237.41"
                            android:centerY="93.62"
                            android:gradientRadius="72.74"
                            android:type="radial">
                            <item android:offset="0.13" android:color="#FFFFF44F"/>
                            <item android:offset="0.53" android:color="#FFFF980E"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M216.4,198.6c-0.2,2.7 -9.7,12 -13,12c-30.8,0 -35.8,18.6 -35.8,18.6c1.4,15.7 12.3,28.6 25.5,35.4c0.6,0.3 1.2,0.6 1.8,0.9c1,0.5 2.1,0.9 3.2,1.3c4.6,1.6 9.3,2.5 14.1,2.7c54,2.5 64.5,-64.6 25.5,-84c10,-1.8 20.3,2.3 26.1,6.4c-9.5,-16.5 -27.1,-27.7 -47.5,-27.7c-1.3,0 -2.5,0.1 -3.8,0.2c-10.9,0.8 -21.3,4.7 -29.8,11.5c1.7,1.4 3.5,3.2 7.4,7.1C197.5,190.2 216.3,197.8 216.4,198.6L216.4,198.6z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="189.41"
                            android:centerY="280.33"
                            android:gradientRadius="96.24"
                            android:type="radial">
                            <item android:offset="0.35" android:color="#FF3A8EE6"/>
                            <item android:offset="0.67" android:color="#FF9059FF"/>
                            <item android:offset="1" android:color="#FFC139E6"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M216.4,198.6c-0.2,2.7 -9.7,12 -13,12c-30.8,0 -35.8,18.6 -35.8,18.6c1.4,15.7 12.3,28.6 25.5,35.4c0.6,0.3 1.2,0.6 1.8,0.9c1,0.5 2.1,0.9 3.2,1.3c4.6,1.6 9.3,2.5 14.1,2.7c54,2.5 64.5,-64.6 25.5,-84c10,-1.8 20.3,2.3 26.1,6.4c-9.5,-16.5 -27.1,-27.7 -47.5,-27.7c-1.3,0 -2.5,0.1 -3.8,0.2c-10.9,0.8 -21.3,4.7 -29.8,11.5c1.7,1.4 3.5,3.2 7.4,7.1C197.5,190.2 216.3,197.8 216.4,198.6L216.4,198.6z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="212.55"
                            android:centerY="199"
                            android:gradientRadius="51.11"
                            android:type="radial">
                            <item android:offset="0.21" android:color="#009059FF"/>
                            <item android:offset="0.97" android:color="#996E008B"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M177.6,172.2c0.8,0.5 1.5,1 2.3,1.5c-2.5,-8.7 -2.6,-17.8 -0.3,-26.5c-11.1,5 -19.8,13.1 -26.1,20.2C154,167.4 169.7,167.1 177.6,172.2L177.6,172.2z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="208.87"
                            android:centerY="120.79"
                            android:gradientRadius="76.52"
                            android:type="radial">
                            <item android:offset="0.1" android:color="#FFFFE226"/>
                            <item android:offset="0.79" android:color="#FFFF7139"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M108.8,222c8.4,49.4 53.2,87.1 104,88.5c47.1,1.4 77.1,-26 89.5,-52.6c8,-17.2 13.6,-45.4 3.4,-73.1l0,0v-0.1c0,-0.1 0,-0.1 0,-0.1s0,0 0,0.2c3.8,25.1 -8.9,49.4 -28.9,65.8v0.1c-38.9,31.6 -76.1,19.1 -83.6,14c-0.5,-0.3 -1,-0.5 -1.6,-0.8c-22.7,-10.8 -32.1,-31.5 -30,-49.2c-19.2,0 -25.7,-16.1 -25.7,-16.1s17.2,-12.3 39.9,-1.6c21,9.9 40.7,1.6 40.7,1.6c0,-0.9 -18.9,-8.4 -26.2,-15.6c-3.9,-3.9 -5.8,-5.7 -7.4,-7.1c-0.9,-0.8 -1.8,-1.4 -2.8,-2.1c-0.7,-0.5 -1.5,-1 -2.3,-1.5c-7.9,-5.1 -23.6,-4.9 -24.1,-4.8l0,0c-4.3,-5.5 -4,-23.4 -3.7,-27.1c0,-0.2 -3.2,1.6 -3.6,1.9c-3.8,2.7 -7.3,5.7 -10.6,9.1c-3.7,3.7 -7.1,7.8 -10.1,12.1l0,0l0,0c-7,9.9 -11.9,21 -14.5,32.8C110.7,196.5 106.9,213.3 108.8,222L108.8,222z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="278.74"
                            android:centerY="70.41"
                            android:gradientRadius="365.3"
                            android:type="radial">
                            <item android:offset="0.11" android:color="#FFFFF44F"/>
                            <item android:offset="0.46" android:color="#FFFF980E"/>
                            <item android:offset="0.72" android:color="#FFFF3647"/>
                            <item android:offset="0.9" android:color="#FFE31587"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M254.4,179.3c4,3.9 7.3,8.3 10.1,13.1c0.6,0.5 1.2,0.9 1.6,1.4c24.6,22.6 11.7,54.6 10.7,56.9c20,-16.5 32.7,-40.8 28.9,-65.8c-12.3,-30.5 -33,-42.8 -50,-69.7c-0.9,-1.4 -1.7,-2.7 -2.6,-4.1c-0.4,-0.7 -0.8,-1.5 -1.2,-2.3c-0.7,-1.4 -1.3,-2.8 -1.6,-4.3c0,-0.1 -0.1,-0.3 -0.2,-0.3c0,0 -0.1,0 -0.2,0l0,0c0,0 0,0 -0.1,0C245.5,106.2 220.7,145.6 254.4,179.3L254.4,179.3z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="247.82"
                            android:centerY="76.43"
                            android:gradientRadius="240.17"
                            android:type="radial">
                            <item android:offset="0" android:color="#FFFFF44F"/>
                            <item android:offset="0.3" android:color="#FFFF980E"/>
                            <item android:offset="0.57" android:color="#FFFF3647"/>
                            <item android:offset="0.74" android:color="#FFE31587"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M266,193.8c-0.5,-0.5 -1,-0.9 -1.6,-1.4c-0.2,-0.2 -0.5,-0.3 -0.7,-0.5c-5.8,-4.1 -16.1,-8.1 -26.1,-6.4c39,19.5 28.5,86.6 -25.5,84c-4.8,-0.2 -9.6,-1.1 -14.1,-2.7c-1.1,-0.4 -2.1,-0.9 -3.2,-1.3c-0.6,-0.3 -1.2,-0.5 -1.8,-0.9c0,0 0,0 0.1,0c7.5,5.1 44.7,17.7 83.6,-14v-0.1C277.8,248.4 290.6,216.4 266,193.8L266,193.8z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="209.65"
                            android:centerY="148.39"
                            android:gradientRadius="209.74"
                            android:type="radial">
                            <item android:offset="0.14" android:color="#FFFFF44F"/>
                            <item android:offset="0.48" android:color="#FFFF980E"/>
                            <item android:offset="0.66" android:color="#FFFF3647"/>
                            <item android:offset="0.9" android:color="#FFE31587"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M167.5,229.2c0,0 5,-18.6 35.8,-18.6c3.3,0 12.8,-9.3 13,-12s-19.7,8.3 -40.7,-1.6c-22.6,-10.6 -39.9,1.6 -39.9,1.6s6.5,16.1 25.7,16.1c-2,17.7 7.3,38.4 30,49.2c0.5,0.2 1,0.5 1.5,0.7C179.8,257.9 168.9,245 167.5,229.2z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="264.09"
                            android:centerY="160.45"
                            android:gradientRadius="252.1"
                            android:type="radial">
                            <item android:offset="0.09" android:color="#FFFFF44F"/>
                            <item android:offset="0.63" android:color="#FFFF980E"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M316.4,179.2c-4.7,-11.3 -14.2,-23.5 -21.7,-27.4c6.1,11.9 9.6,23.9 11,32.9c0,0 0,0 0,0.2c-12.3,-30.5 -33,-42.8 -50,-69.7c-0.9,-1.4 -1.7,-2.7 -2.6,-4.1c-0.4,-0.7 -0.8,-1.5 -1.2,-2.3c-0.7,-1.4 -1.3,-2.8 -1.6,-4.3c0,-0.1 -0.1,-0.3 -0.2,-0.3c0,0 -0.1,0 -0.2,0l0,0c0,0 0,0 -0.1,0l0,0c-27.2,16 -36.5,45.4 -37.3,60.2c1.3,-0.1 2.5,-0.2 3.8,-0.2c20.3,0 38,11.2 47.5,27.7c-5.8,-4.1 -16.1,-8.1 -26.1,-6.4c39,19.5 28.5,86.6 -25.5,84c-4.8,-0.2 -9.6,-1.1 -14.1,-2.7c-1.1,-0.4 -2.1,-0.9 -3.2,-1.3c-0.6,-0.3 -1.2,-0.5 -1.8,-0.9c0,0 0,0 0.1,0c-0.5,-0.3 -1,-0.5 -1.6,-0.8c0.5,0.2 1,0.5 1.5,0.7c-13.3,-6.9 -24.2,-19.7 -25.5,-35.4c0,0 5,-18.6 35.8,-18.6c3.3,0 12.8,-9.3 13,-12c0,-0.9 -18.9,-8.4 -26.2,-15.6c-3.9,-3.9 -5.8,-5.7 -7.4,-7.1c-0.9,-0.8 -1.8,-1.4 -2.8,-2.1c-2.5,-8.7 -2.6,-17.8 -0.3,-26.5c-11.1,5 -19.8,13.1 -26.1,20.2l0,0c-4.3,-5.5 -4,-23.4 -3.7,-27.1c0,-0.2 -3.2,1.6 -3.6,1.9c-3.8,2.7 -7.3,5.7 -10.6,9.1c-3.7,3.7 -7.1,7.8 -10.1,12.1l0,0l0,0c-7,9.9 -11.9,21 -14.5,32.8c0,0.2 -0.1,0.5 -0.1,0.7c-0.2,0.9 -1.1,5.8 -1.3,6.9c0,0.1 0,-0.1 0,0c-0.9,5 -1.4,10.1 -1.6,15.1c0,0.2 0,0.4 0,0.5c0,59.7 48.5,108.2 108.3,108.2c53.6,0 98.1,-38.9 106.8,-90c0.2,-1.4 0.3,-2.8 0.5,-4.2C325.6,215 323.2,195.5 316.4,179.2zM305.7,184.7v0.1l0,0V184.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="285.98"
                            android:startY="140.07"
                            android:endX="151.82"
                            android:endY="296.61"
                            android:type="linear">
                            <item android:offset="0.17" android:color="#CCFFF44F"/>
                            <item android:offset="0.6" android:color="#00FFF44F"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M319.1,269.9h-72.8c-17.2,0 -31.2,14 -31.2,31.2l0,0v15.2c0,2.3 1.9,4.2 4.2,4.2h72.8c17.2,0 31.2,-14 31.2,-31.2v-23.7C323.3,268 321.4,269.9 319.1,269.9L319.1,269.9z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="227.01"
                            android:startY="274.49"
                            android:endX="325.95"
                            android:endY="316.35"
                            android:type="linear">
                            <item android:offset="0" android:color="#FF54FFBD"/>
                            <item android:offset="1" android:color="#FF00DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M247.6,295c1.5,-0.9 2.5,-2.5 2.4,-4.2c0,-3.7 -2.6,-5.9 -7.3,-5.9H234v21h8.7c4.6,0 7.5,-2.1 7.5,-6.2C250.4,297.4 249.4,295.8 247.6,295L247.6,295zM238.1,288.4h4.8c2.1,0 3.1,0.9 3.1,2.3s-0.9,2.5 -3.1,2.5h-4.8V288.4zM242.9,302.2h-4.8V297h4.6c2.6,0 3.5,0.9 3.5,2.6C246.3,301.1 245.1,302.2 242.9,302.2L242.9,302.2zM254.3,305.8h14.1V302h-10.1v-4.8h10.1v-3.8h-10.1v-4.7h10.1v-3.8h-14.1L254.3,305.8L254.3,305.8zM287.4,284.9h-15.6v3.7h5.8v17.2h4v-17.3h5.8L287.4,284.9zM298.3,284.9h-4l-7.9,21h4.1l1.4,-3.8h8.7l1.4,3.8h4.1L298.3,284.9zM293.3,298.3l3.1,-8.3l3,8.3H293.3z"
                    android:fillColor="#20123A"/>
            </group>
        </vector>
    </aapt:attr>
    <target
        android:name="a1_t">
        <aapt:attr
            name="android:animation">
            <set>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="300"
                    android:valueFrom="0.75"
                    android:valueTo="0.75"
                    android:valueType="floatType"
                    android:interpolator="@android:interpolator/linear"/>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="700"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="300"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="1000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="2000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="3000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="4000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="5000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="6000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="7000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="8000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="9000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="10000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="11000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="300"
                    android:valueFrom="0.75002400000000002"
                    android:valueTo="0.75002400000000002"
                    android:valueType="floatType"
                    android:interpolator="@android:interpolator/linear"/>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="700"
                    android:valueFrom="0.75002400000000002"
                    android:valueTo="1"
                    android:startOffset="300"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="1000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="2000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="3000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="4000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="5000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="6000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="7000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="8000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="9000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="10000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="11000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
</animated-vector>
