/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

plugins {
    id "org.gradle.kotlin.kotlin-dsl" version "4.2.1"
}

kotlin {
    jvmToolchain(17)
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    if (project.hasProperty("centralRepo")) {
        maven {
            name "MavenCentral"
            url project.property("centralRepo")
            allowInsecureProtocol true // Local Nexus in CI uses HTTP
        }
    } else {
        mavenCentral()
    }
}

gradlePlugin {
    plugins.register("FenixDependenciesPlugin") {
        id = "FenixDependenciesPlugin"
        implementationClass = "FenixDependenciesPlugin"
    }
}
