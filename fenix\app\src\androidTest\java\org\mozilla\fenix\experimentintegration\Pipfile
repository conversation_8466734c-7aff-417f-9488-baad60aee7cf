# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

[[source]]
url = "https://pypi.python.org/simple"
verify_ssl = true
name = "pypi"

[packages]
pydantic = "*"
pytest = "*"
pytest-html = "*"
pytest-metadata = "*"
pytest-variables = "*"
pyyaml = "*"
requests = "*"

[dev-packages]
black = "*"
flake8 = "*"

[requires]
python_version = "3.11"
