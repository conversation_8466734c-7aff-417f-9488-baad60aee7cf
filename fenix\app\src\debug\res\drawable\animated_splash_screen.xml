<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt" >
    <aapt:attr name="android:drawable">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:aapt="http://schemas.android.com/aapt"
            android:width="432dp"
            android:height="432dp"
            android:viewportWidth="432"
            android:viewportHeight="432">
            <group
                android:name="a1_t"
                android:pivotX="216"
                android:pivotY="216">
                <path
                    android:pathData="M155.3,238.8l8.3,23.3l12.7,17l29.5,5.7l14.1,1.2l24.1,-6.1l10.4,-9.9L155.3,238.8z"
                    android:fillColor="#09204D"/>
                <path
                    android:pathData="M280.6,204.4l-2.9,-9.8l-8.4,-16l-9.6,-10.2l-10.3,-7.4l-8.9,-3.4l-10.7,-3.8l-17.7,-1.2l-11.1,2.1l-9,2.7l-16.1,9.4l-10,11.3l-9.2,15l-3.6,12.9l-0.7,19.4l3.4,13.3l7.8,15.1l5.4,7.4l7.6,6.9l9.3,6.4l11.6,5l19.2,2.3l10.1,0.3l14.9,-4.2l13.3,-7.4l8.5,-7.4l6.7,-8l9.4,-18.5l2.2,-12.7L280.6,204.4z"
                    android:fillColor="#09204D"/>
                <path
                    android:pathData="M212.3,161.5l-8,7.9l13,5.2l9.5,-11.9L212.3,161.5z"
                    android:fillColor="#0E56D7"/>
                <path
                    android:pathData="M217.3,174.6l24.9,-0.8l-15.3,-11.1L217.3,174.6z"
                    android:fillColor="#1053D1"/>
                <path
                    android:pathData="M240.2,162.6l-13.4,0.1l15.3,11.1l14.1,-1L240.2,162.6z"
                    android:fillColor="#0B4FBF"/>
                <path
                    android:pathData="M256.3,172.9l-8,-11.2l11.2,6.7L256.3,172.9z"
                    android:fillColor="#0B48B0"/>
                <path
                    android:pathData="M267,179.8l-10.7,-6.9l3,-4.4L267,179.8z"
                    android:fillColor="#0C45A7"/>
                <path
                    android:pathData="M256.3,172.9l-1.9,7.7l9.7,3.9L256.3,172.9z"
                    android:fillColor="#0F46AF"/>
                <path
                    android:pathData="M256.3,172.9l10.7,6.9l-2.7,4.7L256.3,172.9z"
                    android:fillColor="#0C47AD"/>
                <path
                    android:pathData="M242.2,173.8l14.1,-1l-1.9,7.7L242.2,173.8z"
                    android:fillColor="#0E4FC3"/>
                <path
                    android:pathData="M242.2,173.8L231,183.9l14.4,7.1L242.2,173.8z"
                    android:fillColor="#174DCA"/>
                <path
                    android:pathData="M217.3,174.6l24.9,-0.8L231,183.9L217.3,174.6z"
                    android:fillColor="#1650D4"/>
                <path
                    android:pathData="M254.6,180.6l6.8,13.3l2.9,-9.2L254.6,180.6z"
                    android:fillColor="#1246B2"/>
                <path
                    android:pathData="M261.4,193.7l2.9,-9.2l7.2,17L261.4,193.7z"
                    android:fillColor="#133EA1"/>
                <path
                    android:pathData="M264.4,184.4l10.9,8l-3.7,8.9L264.4,184.4z"
                    android:fillColor="#103D98"/>
                <path
                    android:pathData="M267,179.8l2.2,-1.1l6.1,13.6L267,179.8z"
                    android:fillColor="#0B3785"/>
                <path
                    android:pathData="M275.2,192.3l2.5,2.4l-8.4,-16L275.2,192.3z"
                    android:fillColor="#0B3685"/>
                <path
                    android:pathData="M275.2,192.3l-8.1,-12.7l-2.7,4.7L275.2,192.3zM259.5,168.5l7.7,11.3l2.2,-1.1L259.5,168.5z"
                    android:fillColor="#0C3B91"/>
                <path
                    android:pathData="M280.1,212.2l-2.5,-17.6l2.8,9.8L280.1,212.2L280.1,212.2z"
                    android:fillColor="#0B3279"/>
                <path
                    android:pathData="M275.2,192.3l4.8,19.7l-2.5,-17.6L275.2,192.3z"
                    android:fillColor="#0F388D"/>
                <path
                    android:pathData="M271.5,201.4l8.5,10.8l-4.8,-19.7L271.5,201.4z"
                    android:fillColor="#0D3481"/>
                <path
                    android:pathData="M280,212.2l0.6,-7.8l0.6,19.6L280,212.2L280,212.2z"
                    android:fillColor="#0B2E71"/>
                <path
                    android:pathData="M278.6,227.8l2.7,-3.5l-1.2,-12.1L278.6,227.8z"
                    android:fillColor="#0E2F77"/>
                <path
                    android:pathData="M279.1,236.7l-2.1,1l4.1,-13.5L279.1,236.7z"
                    android:fillColor="#0E2969"/>
                <path
                    android:pathData="M277.1,237.8l4.1,-13.5l-2.7,3.5L277.1,237.8z"
                    android:fillColor="#0C2A69"/>
                <path
                    android:pathData="M278.6,227.8l1.5,-15.5l-4.4,5.2L278.6,227.8z"
                    android:fillColor="#123180"/>
                <path
                    android:pathData="M277.1,237.8l1.6,-10l-8.4,14.2L277.1,237.8z"
                    android:fillColor="#112B70"/>
                <path
                    android:pathData="M271.5,201.4l8.5,10.8l-4.4,5.2L271.5,201.4z"
                    android:fillColor="#12378C"/>
                <path
                    android:pathData="M278.6,227.8l-2.9,-10.4l-4.4,12.9L278.6,227.8z"
                    android:fillColor="#122E7B"/>
                <path
                    android:pathData="M269.7,255.2l7.4,-17.5l-6.9,12.8L269.7,255.2z"
                    android:fillColor="#11296C"/>
                <path
                    android:pathData="M270.2,250.5l6.9,-12.8l-7,4.2L270.2,250.5z"
                    android:fillColor="#142A72"/>
                <path
                    android:pathData="M277.1,237.8l-7.4,17.5l9.4,-18.5L277.1,237.8zM263,263.1l6.7,-8l0.5,-4.7L263,263.1zM254.5,270.5l8.5,-7.4l-9,4.1L254.5,270.5z"
                    android:fillColor="#112768"/>
                <path
                    android:pathData="M204.4,275.5l-8.6,2.2l0.3,-6.1L204.4,275.5z"
                    android:fillColor="#1C226A"/>
                <path
                    android:pathData="M156.2,193.1l2.5,-0.9l-6.1,13.6L156.2,193.1z"
                    android:fillColor="#182B7A"/>
                <path
                    android:pathData="M152.6,206l4.5,-0.9l1.5,-12.9L152.6,206z"
                    android:fillColor="#1F308D"/>
                <path
                    android:pathData="M152.9,222.5l-0.3,-16.5l4.5,-0.9L152.9,222.5z"
                    android:fillColor="#1E2B7F"/>
                <path
                    android:pathData="M254.6,180.6l-9.1,10.5l16,2.7L254.6,180.6zM174.4,169.8l11.8,-8l-10.5,5.2L174.4,169.8z"
                    android:fillColor="#1346B5"/>
                <path
                    android:pathData="M175.7,166.8l16.1,-9.4l-5.6,4.2L175.7,166.8"
                    android:fillColor="#1244AC"/>
                <path
                    android:pathData="M186.2,161.7l14.7,-7.1l-9,2.7L186.2,161.7z"
                    android:fillColor="#0E4AB6"/>
                <path
                    android:pathData="M242.2,173.8l3.1,17.3l9.1,-10.5L242.2,173.8zM195,160.8l13.6,-5.5l-8,-0.7L195,160.8z"
                    android:fillColor="#0E4BBA"/>
                <path
                    android:pathData="M211.9,152.7l-11.1,2.1l8,0.7L211.9,152.7z"
                    android:fillColor="#0A4EBC"/>
                <path
                    android:pathData="M222.5,155.2l-13.6,0.1l3.1,-2.7"
                    android:fillColor="#0C54CC"/>
                <path
                    android:pathData="M248.3,161.7l-8,0.9l16.1,10.3L248.3,161.7zM222.5,155.2l17.9,2.4l-10.7,-3.8L222.5,155.2z"
                    android:fillColor="#0B4BB4"/>
                <path
                    android:pathData="M248.3,161.7l-8,-4.1l8.9,3.4L248.3,161.7z"
                    android:fillColor="#0C3889"/>
                <path
                    android:pathData="M155.3,238.8l5.3,4.3l-7.9,-20.6L155.3,238.8zM228.2,279.7l4.6,-10.9l-16.9,4.5L228.2,279.7zM232.8,268.8l14,3.9l0.9,-10.9L232.8,268.8z"
                    android:fillColor="#202575"/>
                <path
                    android:pathData="M254,267.2l-6.2,-5.4l15,-10L254,267.2z"
                    android:fillColor="#1B2773"/>
                <path
                    android:pathData="M262.7,251.8l-8.7,15.4l9,-4.1l7.2,-12.7L262.7,251.8z"
                    android:fillColor="#15276D"/>
                <path
                    android:pathData="M249.2,161.1l-0.9,0.7l11.2,6.7L249.2,161.1zM240.2,162.6l8,-0.9l-8,-4.1V162.6z"
                    android:fillColor="#0C409D"/>
                <path
                    android:pathData="M211.9,152.7l10.5,2.7l7.2,-1.5L211.9,152.7zM240.2,157.6l-17.9,-2.4l18,7.4L240.2,157.6z"
                    android:fillColor="#0B4CB7"/>
                <path
                    android:pathData="M222.5,155.2l4.3,7.5l13.4,-0.1L222.5,155.2z"
                    android:fillColor="#0B55CE"/>
                <path
                    android:pathData="M222.5,155.2l4.3,7.5l-14.5,-1.2L222.5,155.2z"
                    android:fillColor="#0A52C5"/>
                <path
                    android:pathData="M208.7,155.3l3.4,6.2l10.2,-6.3L208.7,155.3z"
                    android:fillColor="#0951C4"/>
                <path
                    android:pathData="M195,160.8l13.6,-5.5l3.4,6.2L195,160.8z"
                    android:fillColor="#0B50C4"/>
                <path
                    android:pathData="M186.2,161.7l8.9,-0.8l5.8,-6.2L186.2,161.7zM195,160.8l9.2,8.4l8,-7.9L195,160.8z"
                    android:fillColor="#0F4DBF"/>
                <path
                    android:pathData="M195,160.8L179.9,175l6.2,-13.4L195,160.8z"
                    android:fillColor="#1546B6"/>
                <path
                    android:pathData="M179.9,175.1l9.6,1.7l5.5,-15.8L179.9,175.1z"
                    android:fillColor="#1549BD"/>
                <path
                    android:pathData="M189.5,176.6l5.5,-15.8l9.2,8.4L189.5,176.6z"
                    android:fillColor="#144BC3"/>
                <path
                    android:pathData="M165.4,178.1l0.6,5.7l8.1,-14L165.4,178.1z"
                    android:fillColor="#173794"/>
                <path
                    android:pathData="M158.7,192.2l6.9,-14.1l0.6,5.7L158.7,192.2z"
                    android:fillColor="#192F84"/>
                <path
                    android:pathData="M166.1,183.8l8.1,-14l5.6,5.3L166.1,183.8z"
                    android:fillColor="#1A389B"/>
                <path
                    android:pathData="M165.5,196.1l0.5,-12.3l-7.6,8.4L165.5,196.1z"
                    android:fillColor="#1D3089"/>
                <path
                    android:pathData="M165.5,196.1l0.5,-12.3l9.7,8L165.5,196.1z"
                    android:fillColor="#1E389F"/>
                <path
                    android:pathData="M175.8,191.7l4,-16.7l-13.7,8.7L175.8,191.7z"
                    android:fillColor="#1C40AF"/>
                <path
                    android:pathData="M175.8,191.7l4,-16.7l9.6,1.7L175.8,191.7z"
                    android:fillColor="#1E41B7"/>
                <path
                    android:pathData="M157.2,205.2l1.5,-12.9l7,3.7L157.2,205.2z"
                    android:fillColor="#192C7D"/>
                <path
                    android:pathData="M228.2,279.7l13.1,-2.1l5.8,-5L228.2,279.7zM151.9,225.4l1,-2.9l-0.3,-16.5L151.9,225.4zM157.2,205.2l-4.3,17.4l7.9,20.6l-3.8,-24L157.2,205.2z"
                    android:fillColor="#162467"/>
                <path
                    android:pathData="M160.6,243.1l6.5,0.9l-4.5,-10.5L160.6,243.1z"
                    android:fillColor="#1A236A"/>
                <path
                    android:pathData="M254,267.2l-7.1,5.5l-5.8,5l13.3,-7.4L254,267.2zM216.2,281.7l10.1,0.3l14.9,-4.2l-13.1,2.1L216.2,281.7zM166.6,255.1l6.8,1.3L167,244L166.6,255.1z"
                    android:fillColor="#132668"/>
                <path
                    android:pathData="M196.1,271.7l-11.5,-8.7l-4.7,4.5l15.9,10.3L196.1,271.7zM155.3,238.8l7.8,15.1l-2.5,-10.8L155.3,238.8zM151.9,225.4l3.4,13.3l-2.6,-16.3L151.9,225.4zM215.9,273.3l-19.8,-1.9l8.3,3.9l-8.6,2.2l-10.4,-3.2l11.6,5l19.2,2.3l12.1,-2L215.9,273.3zM167.1,244l-6.5,-0.9l6,12L167.1,244zM156.9,219.1l3.8,24l2,-9.6L156.9,219.1zM173.4,256.3l-6.8,-1.3l-3.5,-1.2l5.4,7.4l7.6,6.9l9.3,6.4l-5.5,-7.1L173.4,256.3z"
                    android:fillColor="#1B2268"/>
                <path
                    android:pathData="M157.2,205.2l9,14l-0.7,-23.2L157.2,205.2z"
                    android:fillColor="#212A81"/>
                <path
                    android:pathData="M156.9,219.2l5.7,14.2l3.6,-14.2H156.9z"
                    android:fillColor="#21267A"/>
                <path
                    android:pathData="M162.6,233.5l3.6,-14.2l4.5,11.3L162.6,233.5z"
                    android:fillColor="#262781"/>
                <path
                    android:pathData="M167.1,244l17.5,10.8l-13.7,-24.2L167.1,244z"
                    android:fillColor="#272379"/>
                <path
                    android:pathData="M163.1,253.9l3.5,1.2l-6,-12L163.1,253.9zM201.6,260.1l-5.5,11.5l19.8,1.9L201.6,260.1zM167.1,244l6.4,12.4l6.4,11l4.7,-4.5l-0.1,-8L167.1,244z"
                    android:fillColor="#1E226D"/>
                <path
                    android:pathData="M247.8,261.7l-0.9,10.9l7.1,-5.5L247.8,261.7zM217.4,260.3l-1.5,13.2l16.9,-4.5l-0.6,-14.7L217.4,260.3z"
                    android:fillColor="#222475"/>
                <path
                    android:pathData="M156.9,219.2h9.3l-9,-14L156.9,219.2zM232.2,254.1l0.6,14.7l14.9,-7.1L232.2,254.1z"
                    android:fillColor="#23277D"/>
                <path
                    android:pathData="M232.2,254.1l9.6,-8.7l6,16.5L232.2,254.1z"
                    android:fillColor="#1E2776"/>
                <path
                    android:pathData="M247.8,261.7l7.4,-22.1l-13.3,5.6L247.8,261.7z"
                    android:fillColor="#222B86"/>
                <path
                    android:pathData="M262.7,251.8l-14.9,9.9l7.4,-22.1L262.7,251.8z"
                    android:fillColor="#192974"/>
                <path
                    android:pathData="M270.2,241.9l-15,-2.3l7.8,12.1l7.5,-1.5L270.2,241.9z"
                    android:fillColor="#172A75"/>
                <path
                    android:pathData="M271.2,230l-16.1,9.6l15,2.3l8.4,-14.2L271.2,230z"
                    android:fillColor="#172D7D"/>
                <path
                    android:pathData="M158.7,192.2l6.9,-14.1l-9.2,15L158.7,192.2zM265.2,209.9l6,20.2l4.4,-12.9L265.2,209.9z"
                    android:fillColor="#18338A"/>
                <path
                    android:pathData="M275.7,217.4l-4.1,-16l-6.4,8.4L275.7,217.4z"
                    android:fillColor="#153791"/>
                <path
                    android:pathData="M255.1,239.8l16.1,-9.6l-15.6,-7L255.1,239.8z"
                    android:fillColor="#1B318B"/>
                <path
                    android:pathData="M255.1,239.8L240.5,225l1.4,20.3L255.1,239.8z"
                    android:fillColor="#242D8D"/>
                <path
                    android:pathData="M232.2,254.1l-11.2,-9.9l20.8,1.2L232.2,254.1z"
                    android:fillColor="#2A2B8F"/>
                <path
                    android:pathData="M167.1,244l3.6,-13.4l-8.1,2.9L167.1,244zM221,244.2l-3.6,16.1l14.8,-6.2L221,244.2z"
                    android:fillColor="#27267F"/>
                <path
                    android:pathData="M197.7,247.4l-10.8,-5.5l-2.6,12.9l17,5.3l15.8,0.2L197.7,247.4z"
                    android:fillColor="#2D2381"/>
                <path
                    android:pathData="M217.4,260.3l3.6,-16.1l-23.3,3.2L217.4,260.3z"
                    android:fillColor="#2F2485"/>
                <path
                    android:pathData="M166.2,219.1l4.6,-14.8l-5.3,-8.2L166.2,219.1z"
                    android:fillColor="#233196"/>
                <path
                    android:pathData="M175.8,191.7l20.4,-6l-10.8,22.1L175.8,191.7z"
                    android:fillColor="#293CB7"/>
                <path
                    android:pathData="M255.1,239.8L240.5,225l15.1,-1.9L255.1,239.8z"
                    android:fillColor="#25339C"/>
                <path
                    android:pathData="M271.2,230l-6,-20.2l-9.6,13.3L271.2,230z"
                    android:fillColor="#19348F"/>
                <path
                    android:pathData="M241.8,245.3l-16.7,-16.2l15.3,-4.2L241.8,245.3z"
                    android:fillColor="#2A309A"/>
                <path
                    android:pathData="M221,244.2l20.8,1.2l-16.7,-16.2L221,244.2z"
                    android:fillColor="#302B96"/>
                <path
                    android:pathData="M221,244.2l-11.4,-9.2l15.6,-5.7L221,244.2z"
                    android:fillColor="#3630A7"/>
                <path
                    android:pathData="M174.4,169.8l1.3,-2.7l-10,11.3L174.4,169.8zM261.4,193.7l3.7,16.2l6.4,-8.4L261.4,193.7z"
                    android:fillColor="#173DA2"/>
                <path
                    android:pathData="M175.8,191.7l9.6,16.1l-14.5,-3.4L175.8,191.7z"
                    android:fillColor="#2A36A8"/>
                <path
                    android:pathData="M170.8,204.3l14.5,3.4l-9.8,15.5L170.8,204.3z"
                    android:fillColor="#312E9F"/>
                <path
                    android:pathData="M196.3,185.8l8,-16.4l-14.7,7.4L196.3,185.8z"
                    android:fillColor="#184DCD"/>
                <path
                    android:pathData="M166.2,219.1l9.3,4.1l-4.8,7.2L166.2,219.1z"
                    android:fillColor="#272986"/>
                <path
                    android:pathData="M175.7,223.4l-4.8,7.2l13.6,24.2l2.6,-12.9L175.7,223.4z"
                    android:fillColor="#2C2584"/>
                <path
                    android:pathData="M186.9,241.9l1.6,-22.6l-13,3.9L186.9,241.9z"
                    android:fillColor="#32268B"/>
                <path
                    android:pathData="M186.9,241.9l22.6,-7.2l-21,-15.4L186.9,241.9z"
                    android:fillColor="#372999"/>
                <path
                    android:pathData="M209.5,234.8l-2.3,-17.5l-18.8,2.1L209.5,234.8z"
                    android:fillColor="#392DA5"/>
                <path
                    android:pathData="M207.4,217.5l15.9,-6l1.9,17.8L207.4,217.5z"
                    android:fillColor="#3338BC"/>
                <path
                    android:pathData="M197.7,247.4l23.3,-3.2l-11.4,-9.2L197.7,247.4z"
                    android:fillColor="#362896"/>
                <path
                    android:pathData="M197.7,247.4l-10.8,-5.5l22.6,-7.2L197.7,247.4z"
                    android:fillColor="#35268F"/>
                <path
                    android:pathData="M196.3,185.8l13.7,1.5l-5.8,-17.9L196.3,185.8z"
                    android:fillColor="#2648D3"/>
                <path
                    android:pathData="M217.3,174.6l-13,-5.2l5.8,17.9L217.3,174.6z"
                    android:fillColor="#1057DC"/>
                <path
                    android:pathData="M217.3,174.6l13.6,9.4l-20.8,3.2L217.3,174.6z"
                    android:fillColor="#1653DA"/>
                <path
                    android:pathData="M210.1,187.1l20.4,10.9l-7.3,13.4L210.1,187.1z"
                    android:fillColor="#2B46D5"/>
                <path
                    android:pathData="M230.5,198l0.4,-14l-20.8,3.2L230.5,198z"
                    android:fillColor="#244AD7"/>
                <path
                    android:pathData="M230.5,198l0.4,-14l14.4,7.1L230.5,198z"
                    android:fillColor="#2547CF"/>
                <path
                    android:pathData="M210.1,187.1l13.3,24.2l-23.2,-11.6L210.1,187.1z"
                    android:fillColor="#333CC5"/>
                <path
                    android:pathData="M207.4,217.5l15.9,-6l-23.2,-11.6L207.4,217.5z"
                    android:fillColor="#3833B6"/>
                <path
                    android:pathData="M223.4,211.5l20.4,-3.7l-13.3,-9.6L223.4,211.5z"
                    android:fillColor="#2A41C6"/>
                <path
                    android:pathData="M245.3,191.1l-1.7,16.7l-13.3,-9.6L245.3,191.1z"
                    android:fillColor="#2045C1"/>
                <path
                    android:pathData="M245.3,191.1l9.6,13.2l-11.3,3.5L245.3,191.1z"
                    android:fillColor="#1B43B7"/>
                <path
                    android:pathData="M261.4,193.7l-16,-2.7l9.6,13.2L261.4,193.7z"
                    android:fillColor="#1742AF"/>
                <path
                    android:pathData="M180,175.1l6.2,-13.4l-11.8,8L180,175.1zM261.4,193.7l-6.4,10.4l10.1,5.7L261.4,193.7z"
                    android:fillColor="#1740A9"/>
                <path
                    android:pathData="M255.7,223.2l9.6,-13.3l-10.1,-5.7L255.7,223.2z"
                    android:fillColor="#1B3CA4"/>
                <path
                    android:pathData="M255.7,223.2l-0.6,-18.9l-11.3,3.5L255.7,223.2z"
                    android:fillColor="#1F3DAD"/>
                <path
                    android:pathData="M175.8,191.7l20.4,-6l-6.8,-9L175.8,191.7zM223.4,211.5l17.2,13.5l3.2,-17.3L223.4,211.5z"
                    android:fillColor="#2543C3"/>
                <path
                    android:pathData="M225.1,229.1l15.3,-4.2l-17.2,-13.5L225.1,229.1z"
                    android:fillColor="#2E37B2"/>
                <path
                    android:pathData="M196.3,185.8l3.8,14l9.9,-12.7L196.3,185.8z"
                    android:fillColor="#2D40C7"/>
                <path
                    android:pathData="M196.3,185.8l-10.8,22.1l14.6,-8L196.3,185.8z"
                    android:fillColor="#3537B8"/>
                <path
                    android:pathData="M200.2,199.9l-11.7,19.5l18.8,-2.1L200.2,199.9z"
                    android:fillColor="#3931B0"/>
                <path
                    android:pathData="M207.4,217.5l2.3,17.5l15.6,-5.7L207.4,217.5zM188.5,219.3l11.7,-19.5l-14.6,8L188.5,219.3z"
                    android:fillColor="#3735B8"/>
                <path
                    android:pathData="M175.7,223.4l9.8,-15.5l3,11.6L175.7,223.4z"
                    android:fillColor="#342892"/>
                <path
                    android:pathData="M185.4,274.4l10.4,3.2l-15.9,-10.3L185.4,274.4zM184.6,262.7l11.5,8.7l5.5,-11.5l-17,-5.3V262.7zM215.9,273.3l1.5,-13.2l-15.8,-0.2L215.9,273.3zM232.8,268.8l-4.6,10.9l18.7,-7.1L232.8,268.8z"
                    android:fillColor="#232372"/>
                <path
                    android:pathData="M166.2,219.1l4.6,-14.8l4.7,18.9L166.2,219.1z"
                    android:fillColor="#2A2F96"/>
                <path
                    android:pathData="M170.8,204.3l4.9,-12.7l-10.2,4.3L170.8,204.3zM243.7,207.7l-3.2,17.3l15.1,-1.9L243.7,207.7z"
                    android:fillColor="#2636A5"/>
                <path
                    android:pathData="M281.5,224.1L281.5,224.1l-0.6,-19.7l0,0l-2.9,-9.8l0,0l-8.4,-16l-9.7,-10.3l0,0l-10.3,-7.4l0,0l-8.9,-3.4h-0.1l-10.7,-3.8h-0.1l-17.7,-1.2h-0.1l-11.1,2.1l0,0l-9,2.7h-0.1l-16.1,9.3l-10,11.2l-9.2,15l0,0l-3.6,12.9l0,0l-0.7,19.4v0.1l3.4,13.3l0,0l7.8,15.1l0,0l5.3,7.6l0,0l7.6,6.9l0,0l9.3,6.4l0,0l11.6,5h0.1l19.2,2.3l0,0l10.1,0.3h0.1l14.9,-4.2h0.1l13.3,-7.4l0,0l0,0l8.5,-7.4l6.7,-8l0,0l9.4,-18.5v0l2.1,-12.7l0,0V224.1zM176.1,267.8l-2,-1.8l4.3,3.3L176.1,267.8zM204.4,275.2l-6.9,-3.1l16.1,1.5L204.4,275.2zM207,217.2l-18,2l11,-18.6L207,217.2zM200.7,200.5l21.9,11l-15,5.6L200.7,200.5zM207.2,217.8l2.2,16.5l-19.9,-14.6L207.2,217.8zM208.9,234.7l-21.6,6.9l1.5,-21.6L208.9,234.7zM207.8,218.1l16.8,11.1l-14.6,5.3L207.8,218.1zM207.9,217.6l15,-5.6l1.8,16.8L207.9,217.6zM231.1,198.1l13.9,-6.6l-1.6,15.6L231.1,198.1zM243,207.7l-19.1,3.4l6.9,-12.6L243,207.7zM223.3,210.9l-12.5,-23l19.3,10.2L223.3,210.9zM222.6,210.8l-22,-11l9.4,-12.1L222.6,210.8zM188.6,218.6l-2.7,-10.7l13.4,-7.4L188.6,218.6zM186.7,241L176,223.5l12.2,-3.6L186.7,241zM208.5,235.4l-10.9,11.7l-9.9,-5.1L208.5,235.4zM224.6,229.6l-3.8,13.9l-10.7,-8.6L224.6,229.6zM223.7,212.2l16.2,12.8l-14.4,3.9L223.7,212.2zM223.9,211.7l19.3,-3.5l-3,16.4L223.9,211.7zM244.1,207.4l1.6,-15.5l8.9,12.2L244.1,207.4zM254.8,204.5l0.5,17.8l-11.1,-14.3L254.8,204.5zM230.8,197.5l0.4,-13.3l13.4,6.7L230.8,197.5zM230.2,197.5L211,187.3l19.6,-3L230.2,197.5zM200.3,199.2l-3.5,-13.2l13,1.4L200.3,199.2zM199.8,199.7l-13.6,7.5l10,-20.5L199.8,199.7zM188.1,219.1l-11.9,3.5l9,-14.2L188.1,219.1zM186.1,240.9l-14.8,-10.5l4.4,-6.6L186.1,240.9zM187.1,242.4l9.9,5.1l-12.3,6.8L187.1,242.4zM209.5,235.2l10.7,8.6l-21.7,3L209.5,235.2zM225.2,229.7L241,245l-19.6,-1.2L225.2,229.7zM225.7,229.3l14.4,-3.9l1.4,19.2L225.7,229.3zM243.9,208.4l11.2,14.5l-14.1,1.8L243.9,208.4zM255.4,204.6l9.4,5.3l-8.9,12.4L255.4,204.6zM255.5,204l5.9,-9.5l3.4,14.8L255.5,204zM255,203.6l-8.9,-12.2l14.8,2.5L255,203.6zM231.4,183.9l10.5,-9.5l2.9,16.2L231.4,183.9zM210.6,186.8l6.7,-11.9l12.9,8.7L210.6,186.8zM196.8,185.6l7.6,-15.4l5.4,16.8L196.8,185.6zM185.5,207.3l-9.1,-15.3l19.4,-5.7L185.5,207.3zM175.8,222.7l-4.4,-17.9L185,208L175.8,222.7zM170.7,229.8l-4,-10.1l8.3,3.6L170.7,229.8zM186.7,242.1l-2.4,11.9l-12.8,-22.5L186.7,242.1zM197.5,247.9l3.4,11.8l-15.8,-4.9L197.5,247.9zM220.6,244.6l-3.4,15.2l-18.7,-12.2L220.6,244.6zM241.1,245.5l-8.9,8l-10.4,-9.1L241.1,245.5zM240.7,225.7l13.6,13.8l-12.5,5.3L240.7,225.7zM241,225.2l14.1,-1.8l-0.5,15.6L241,225.2zM265.1,210.5l5.7,19L256,223L265.1,210.5zM261.7,194.4l9.2,7.1l-5.8,7.9L261.7,194.4zM245.9,190.9l8.5,-9.8l6.4,12.3L245.9,190.9zM245.5,190.5l-2.9,-16l11.6,6.3L245.5,190.5zM230.9,183.6l-12.9,-8.7l23.3,-0.7L230.9,183.6zM210.1,186.5l-5.4,-16.7l12.1,4.8L210.1,186.5zM196.3,185.3l-6.2,-8.3l13.6,-6.8L196.3,185.3zM195.8,185.7l-19,5.6l12.9,-13.9L195.8,185.7zM184.9,207.5l-13.5,-3.1l4.6,-11.8L184.9,207.5zM175.3,222.9l-8.5,-3.7l4.2,-13.5L175.3,222.9zM170.5,230.3L163,233l3.2,-13L170.5,230.3zM183.7,254l-16.3,-10l3.4,-12.7L183.7,254zM200.2,260.1l-15.3,2.5l-0.1,-7.3L200.2,260.1zM198.2,248.2l18.2,11.9l-14.6,-0.2L198.2,248.2zM221.1,244.8l10.5,9.2l-13.8,5.8L221.1,244.8zM241.7,245.8l5.6,15.3l-14.5,-7.3L241.7,245.8zM242.2,245.5l12.4,-5.2l-6.9,20.6L242.2,245.5zM255.9,223.7l14.7,6.6l-15.1,9L255.9,223.7zM265.7,210.6l9.6,6.9l-4.1,11.8L265.7,210.6zM265.6,209.8l5.8,-7.9l3.8,14.6L265.6,209.8zM261.6,193.6l2.7,-8.2l6.4,15.2L261.6,193.6zM261.3,192.9l-6.2,-11.9l8.7,3.5L261.3,192.9zM243.2,174.1l12.9,-0.9l-1.7,6.9L243.2,174.1zM217.9,174.3l9,-11.2l14.4,10.5L217.9,174.3zM217.4,174.1l-4.6,-12.2l13.5,1.1L217.4,174.1zM216.7,174.1l-11.9,-4.8l7.3,-7.3L216.7,174.1zM190.1,176l5.1,-14.7l8.6,8L190.1,176zM176.3,190.8l3.7,-15.3l8.8,1.5L176.3,190.8zM170.8,203.7l-4.8,-7.6l9.2,-3.9L170.8,203.7zM170.6,204.4l-4,13.1l-0.6,-20.3L170.6,204.4zM165.8,219.5l-3.3,13.1l-5.2,-13.1H165.8zM157.2,218.9l0.3,-12.9l8.2,12.9H157.2zM170.5,231l-3.3,12.2l-4,-9.4L170.5,231zM183.7,254.7l-10,1.5l-5.8,-11.3L183.7,254.7zM184.2,255.2l0.1,7.2l-9.9,-5.7L184.2,255.2zM201.1,260.6l-5.1,10.7l-10.6,-8.1L201.1,260.6zM201.7,260.7l13.4,12.5l-18.7,-1.7L201.7,260.7zM202.4,260.5l14.7,0.2l-1.4,12.3L202.4,260.5zM231.9,254.6l0.6,13.7l-14.4,-8L231.9,254.6zM232.5,254.7l14.5,7.3l-14,6.6L232.5,254.7zM255.2,240.4l7.3,11.4l-14,9.3L255.2,240.4zM255.7,240.1l13.9,2.1l-6.9,9L255.7,240.1zM255.9,239.7l14.9,-8.9l-1,11L255.9,239.7zM275.7,218.4l2.7,9.2l-6.7,2.2L275.7,218.4zM272.1,202.5l7.6,9.5l-3.8,4.6L272.1,202.5zM265.1,185.3l9.8,7.3l-3.3,8L265.1,185.3zM255,180.4l1.6,-6.8l7.1,10.2L255,180.4zM242.4,173.5l-1.8,-10.3l14.7,9.4L242.4,173.5zM241.7,173.2l-14,-10.2l12.4,-0.1L241.7,173.2zM213.2,161.3l9.1,-5.6l3.9,6.7L213.2,161.3zM204.3,169l-8.4,-7.9l15.7,0.6L204.3,169zM189.3,176.3l-8.8,-1.5l13.8,-13.1L189.3,176.3zM175.7,191.3l-9,-7.4l12.9,-8.1L175.7,191.3zM175.3,191.7l-9.3,3.9l0.5,-11.2L175.3,191.7zM165.8,218.3l-8.3,-13.1l7.9,-8.3L165.8,218.3zM162.3,233.6l-1.6,8l-3.1,-19.7L162.3,233.6zM162.7,234.5l3.9,9.1l-5.6,-0.8L162.7,234.5zM172.9,255.9l-5.9,-1.2l0.4,-9.7L172.9,255.9zM184.1,262.8l-4.2,4l-5.7,-9.7L184.1,262.8zM184.6,263.1l10.1,7.9l-14.3,-3.7L184.6,263.1zM217.6,260.8l14.4,8l-15.8,4.2L217.6,260.8zM247.4,262.3l-0.8,10l-13.1,-3.6L247.4,262.3zM261.8,252.8l-8,14l-5.7,-5L261.8,252.8zM269.8,242.9v7.4l-6.5,1.3L269.8,242.9zM271.5,230.3l6.6,-2.2l-7.5,12.6L271.5,230.3zM276,217.5l3.6,-4.3l-1.2,13.2L276,217.5zM271.9,201.4l3.2,-8l4.3,17.7L271.9,201.4zM264.7,184.4l2.4,-4l7.1,11L264.7,184.4zM264.3,183.9l-7,-9.9l9.2,5.9L264.3,183.9zM241.1,162.8l7.1,-0.8l7.1,9.7L241.1,162.8zM226.9,162.4l-3.8,-6.6l15.7,6.5L226.9,162.4zM212.4,161.1l-3,-5.4l12.2,-0.1L212.4,161.1zM211.7,161.2l-15.3,-0.6l12.3,-4.9L211.7,161.2zM180.9,173.9l5.5,-11.9l8,-0.8L180.9,173.9zM179.8,174.6l-5,-4.8l10.6,-7.3L179.8,174.6zM179.5,175.1l-12.5,8l7.5,-12.8L179.5,175.1zM165.3,195.6l-6.3,-3.3l6.7,-7.7L165.3,195.6zM165.1,196.2l-7.7,8.1l1.4,-11.6L165.1,196.2zM156.5,219l-3.1,2.7l3.4,-13.8L156.5,219zM156.6,219.7l3.2,20.5l-6.8,-17.8L156.6,219.7zM166.7,244.3l-0.4,9.6l-5.2,-10.4L166.7,244.3zM173.2,256.6l5.3,9.1l-11,-10.2L173.2,256.6zM195.7,271.8l-0.2,5.3l-13.9,-9L195.7,271.8zM196.3,271.9l7.3,3.3l-7.6,1.9L196.3,271.9zM215.6,273.7l0.2,7.5l-10.5,-5.5L215.6,273.7zM232.3,269.3l-4.2,10l-11.3,-5.9L232.3,269.3zM233,269.2l13.1,3.5l-17.3,6.5L233,269.2zM248,262.4l5.5,4.8l-6.3,4.9L248,262.4zM262.9,252.1l6.4,-1.3l-13.8,14.4L262.9,252.1zM270.5,249.4v-7.2l5.9,-3.5L270.5,249.4zM271,241.1l7.2,-12l-1.4,8.3L271,241.1zM280,215.3l0.9,8.7l-2,2.7L280,215.3zM275.7,193.2l1.7,1.6l1.7,11.8L275.7,193.2zM267.4,179.9l1.6,-0.8l4.4,10L267.4,179.9zM256.7,172.8l2.7,-3.8l6.6,9.7L256.7,172.8zM256.3,172.4l-7.1,-9.6l9.7,5.8L256.3,172.4zM240.5,162.3v-4.2l6.8,3.4L240.5,162.3zM240,162.2l-15.4,-6.4l15.3,2.1L240,162.2zM209.5,155l2.5,-2.2l8,2.1L209.5,155zM196.2,160l4.7,-5.1l6.6,0.6L196.2,160zM194.9,160.5l-7.3,0.7l11.9,-5.7L194.9,160.5zM175.1,169l0.8,-1.9l6.7,-3.2L175.1,169zM166.2,182.9l-0.5,-4.7l7.3,-6.9L166.2,182.9zM165.7,183.7l-6,6.8l5.5,-11.4L165.7,183.7zM156.9,204.9l-3.8,0.7l5,-11.5L156.9,204.9zM156.7,205.6l-3.6,14.7l-0.2,-13.9L156.7,205.6zM160,242.2l-4.3,-3.5l-2.2,-13.4L160,242.2zM166,254.6l-2.7,-0.9l-1.9,-8.2L166,254.6zM177.9,266.1l-9.1,-5l-1.5,-5L177.9,266.1zM179.6,267.5l3.9,5.1l-12.2,-9.5L179.6,267.5zM181.2,268.5l12.8,8.1l-8.2,-2.7L181.2,268.5zM204.3,275.9l9.8,5.1l-17.2,-3.3L204.3,275.9zM216.2,273.8l11,5.8l-10.8,1.8L216.2,273.8zM245.4,273.5l-4.4,3.8l-10.1,1.6L245.4,273.5zM253.8,267.7l0.4,2.5l-5.8,1.8L253.8,267.7zM268.7,252.3l-6,10.6l-7.7,3.4L268.7,252.3zM270.5,250.5l3.9,-7.4l-4.2,10.1L270.5,250.5zM278.8,227.9l1.6,-2.2l-2.5,8L278.8,227.9zM280.3,212.2V212l0.2,-2l0.2,5.1L280.3,212.2zM279.9,209.2l-1.6,-10.9l1.9,6.2L279.9,209.2zM275.5,192.1l-2.7,-6.3l3.8,7.3L275.5,192.1zM267.1,179.4l-5.5,-8l7.1,7.5L267.1,179.4zM248.8,161.7l0.4,-0.3l4.2,2.9L248.8,161.7zM248.3,161.4l-2.8,-1.5l3.1,1.3L248.3,161.4zM224.1,155.1l5.4,-1.1l8,2.8L224.1,155.1zM222.5,154.9l-7.4,-1.9l12.3,0.8L222.5,154.9zM208.5,155l-5.7,-0.5l8,-1.5L208.5,155zM188.5,160.1l3.2,-2.5l5.2,-1.7L188.5,160.1zM186,161.4l-2.7,1.5l4.3,-2.6L186,161.4zM174.1,169.6l-3.3,3.1l3.8,-4.2L174.1,169.6zM158.5,192l-1.5,0.6l5.8,-9.3L158.5,192zM156.4,193.4l1.7,-0.6l-4,9.2L156.4,193.4zM152.5,217.4l0.1,5.1l-0.3,1L152.5,217.4zM152.8,223.8l1.5,9.4l-2.1,-7.8L152.8,223.8zM160.3,243.3l1.9,8.3l-6,-11.8L160.3,243.3zM166.4,255.4l1.4,4.4l-3.8,-5.2L166.4,255.4zM195.6,277.8l0.6,0.9l-5.7,-2.6L195.6,277.8zM197.1,279.1l-0.7,-1.1l12.5,2.5L197.1,279.1zM227.4,280.2l-1.4,1.5l-6.9,-0.2L227.4,280.2zM228.4,280l8,-1.3l-9.3,2.7L228.4,280zM247,272.9l4.7,-1.5l-8.3,4.6L247,272.9zM254.7,269.9l-0.4,-2.5l7,-3.1L254.7,269.9zM269.7,251.7l-0.3,3.2l-4.5,5.5L269.7,251.7zM277.3,238l1.2,-0.6L273,248L277.3,238zM278.8,236.5l-1.4,0.7l2.7,-8.6L278.8,236.5z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="223.08"
                            android:centerY="184.89"
                            android:gradientRadius="77.77"
                            android:type="radial">
                            <item android:offset="0.1" android:color="#6600DDFF"/>
                            <item android:offset="0.9" android:color="#3F7542E5"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M281.5,224.1L281.5,224.1l-0.6,-19.7l0,0l-2.9,-9.8l0,0l-8.4,-16l-9.7,-10.3l0,0l-10.3,-7.4l0,0l-8.9,-3.4h-0.1l-10.7,-3.8h-0.1l-17.7,-1.2h-0.1l-11.1,2.1l0,0l-9,2.7h-0.1l-16.1,9.3l-10,11.2l-9.2,15l0,0l-3.6,12.9l0,0l-0.7,19.4v0.1l3.4,13.3l0,0l7.8,15.1l0,0l5.3,7.6l0,0l7.6,6.9l0,0l9.3,6.4l0,0l11.6,5h0.1l19.2,2.3l0,0l10.1,0.3h0.1l14.9,-4.2h0.1l13.3,-7.4l0,0l0,0l8.5,-7.4l6.7,-8l0,0l9.4,-18.5v0l2.1,-12.7l0,0V224.1zM176.1,267.8l-2,-1.8l4.3,3.3L176.1,267.8zM204.4,275.2l-6.9,-3.1l16.1,1.5L204.4,275.2zM207,217.2l-18,2l11,-18.6L207,217.2zM200.7,200.5l21.9,11l-15,5.6L200.7,200.5zM207.2,217.8l2.2,16.5l-19.9,-14.6L207.2,217.8zM208.9,234.7l-21.6,6.9l1.5,-21.6L208.9,234.7zM207.8,218.1l16.8,11.1l-14.6,5.3L207.8,218.1zM207.9,217.6l15,-5.6l1.8,16.8L207.9,217.6zM231.1,198.1l13.9,-6.6l-1.6,15.6L231.1,198.1zM243,207.7l-19.1,3.4l6.9,-12.6L243,207.7zM223.3,210.9l-12.5,-23l19.3,10.2L223.3,210.9zM222.6,210.8l-22,-11l9.4,-12.1L222.6,210.8zM188.6,218.6l-2.7,-10.7l13.4,-7.4L188.6,218.6zM186.7,241L176,223.5l12.2,-3.6L186.7,241zM208.5,235.4l-10.9,11.7l-9.9,-5.1L208.5,235.4zM224.6,229.6l-3.8,13.9l-10.7,-8.6L224.6,229.6zM223.7,212.2l16.2,12.8l-14.4,3.9L223.7,212.2zM223.9,211.7l19.3,-3.5l-3,16.4L223.9,211.7zM244.1,207.4l1.6,-15.5l8.9,12.2L244.1,207.4zM254.8,204.5l0.5,17.8l-11.1,-14.3L254.8,204.5zM230.8,197.5l0.4,-13.3l13.4,6.7L230.8,197.5zM230.2,197.5L211,187.3l19.6,-3L230.2,197.5zM200.3,199.2l-3.5,-13.2l13,1.4L200.3,199.2zM199.8,199.7l-13.6,7.5l10,-20.5L199.8,199.7zM188.1,219.1l-11.9,3.5l9,-14.2L188.1,219.1zM186.1,240.9l-14.8,-10.5l4.4,-6.6L186.1,240.9zM187.1,242.4l9.9,5.1l-12.3,6.8L187.1,242.4zM209.5,235.2l10.7,8.6l-21.7,3L209.5,235.2zM225.2,229.7L241,245l-19.6,-1.2L225.2,229.7zM225.7,229.3l14.4,-3.9l1.4,19.2L225.7,229.3zM243.9,208.4l11.2,14.5l-14.1,1.8L243.9,208.4zM255.4,204.6l9.4,5.3l-8.9,12.4L255.4,204.6zM255.5,204l5.9,-9.5l3.4,14.8L255.5,204zM255,203.6l-8.9,-12.2l14.8,2.5L255,203.6zM231.4,183.9l10.5,-9.5l2.9,16.2L231.4,183.9zM210.6,186.8l6.7,-11.9l12.9,8.7L210.6,186.8zM196.8,185.6l7.6,-15.4l5.4,16.8L196.8,185.6zM185.5,207.3l-9.1,-15.3l19.4,-5.7L185.5,207.3zM175.8,222.7l-4.4,-17.9L185,208L175.8,222.7zM170.7,229.8l-4,-10.1l8.3,3.6L170.7,229.8zM186.7,242.1l-2.4,11.9l-12.8,-22.5L186.7,242.1zM197.5,247.9l3.4,11.8l-15.8,-4.9L197.5,247.9zM220.6,244.6l-3.4,15.2l-18.7,-12.2L220.6,244.6zM241.1,245.5l-8.9,8l-10.4,-9.1L241.1,245.5zM240.7,225.7l13.6,13.8l-12.5,5.3L240.7,225.7zM241,225.2l14.1,-1.8l-0.5,15.6L241,225.2zM265.1,210.5l5.7,19L256,223L265.1,210.5zM261.7,194.4l9.2,7.1l-5.8,7.9L261.7,194.4zM245.9,190.9l8.5,-9.8l6.4,12.3L245.9,190.9zM245.5,190.5l-2.9,-16l11.6,6.3L245.5,190.5zM230.9,183.6l-12.9,-8.7l23.3,-0.7L230.9,183.6zM210.1,186.5l-5.4,-16.7l12.1,4.8L210.1,186.5zM196.3,185.3l-6.2,-8.3l13.6,-6.8L196.3,185.3zM195.8,185.7l-19,5.6l12.9,-13.9L195.8,185.7zM184.9,207.5l-13.5,-3.1l4.6,-11.8L184.9,207.5zM175.3,222.9l-8.5,-3.7l4.2,-13.5L175.3,222.9zM170.5,230.3L163,233l3.2,-13L170.5,230.3zM183.7,254l-16.3,-10l3.4,-12.7L183.7,254zM200.2,260.1l-15.3,2.5l-0.1,-7.3L200.2,260.1zM198.2,248.2l18.2,11.9l-14.6,-0.2L198.2,248.2zM221.1,244.8l10.5,9.2l-13.8,5.8L221.1,244.8zM241.7,245.8l5.6,15.3l-14.5,-7.3L241.7,245.8zM242.2,245.5l12.4,-5.2l-6.9,20.6L242.2,245.5zM255.9,223.7l14.7,6.6l-15.1,9L255.9,223.7zM265.7,210.6l9.6,6.9l-4.1,11.8L265.7,210.6zM265.6,209.8l5.8,-7.9l3.8,14.6L265.6,209.8zM261.6,193.6l2.7,-8.2l6.4,15.2L261.6,193.6zM261.3,192.9l-6.2,-11.9l8.7,3.5L261.3,192.9zM243.2,174.1l12.9,-0.9l-1.7,6.9L243.2,174.1zM217.9,174.3l9,-11.2l14.4,10.5L217.9,174.3zM217.4,174.1l-4.6,-12.2l13.5,1.1L217.4,174.1zM216.7,174.1l-11.9,-4.8l7.3,-7.3L216.7,174.1zM190.1,176l5.1,-14.7l8.6,8L190.1,176zM176.3,190.8l3.7,-15.3l8.8,1.5L176.3,190.8zM170.8,203.7l-4.8,-7.6l9.2,-3.9L170.8,203.7zM170.6,204.4l-4,13.1l-0.6,-20.3L170.6,204.4zM165.8,219.5l-3.3,13.1l-5.2,-13.1H165.8zM157.2,218.9l0.3,-12.9l8.2,12.9H157.2zM170.5,231l-3.3,12.2l-4,-9.4L170.5,231zM183.7,254.7l-10,1.5l-5.8,-11.3L183.7,254.7zM184.2,255.2l0.1,7.2l-9.9,-5.7L184.2,255.2zM201.1,260.6l-5.1,10.7l-10.6,-8.1L201.1,260.6zM201.7,260.7l13.4,12.5l-18.7,-1.7L201.7,260.7zM202.4,260.5l14.7,0.2l-1.4,12.3L202.4,260.5zM231.9,254.6l0.6,13.7l-14.4,-8L231.9,254.6zM232.5,254.7l14.5,7.3l-14,6.6L232.5,254.7zM255.2,240.4l7.3,11.4l-14,9.3L255.2,240.4zM255.7,240.1l13.9,2.1l-6.9,9L255.7,240.1zM255.9,239.7l14.9,-8.9l-1,11L255.9,239.7zM275.7,218.4l2.7,9.2l-6.7,2.2L275.7,218.4zM272.1,202.5l7.6,9.5l-3.8,4.6L272.1,202.5zM265.1,185.3l9.8,7.3l-3.3,8L265.1,185.3zM255,180.4l1.6,-6.8l7.1,10.2L255,180.4zM242.4,173.5l-1.8,-10.3l14.7,9.4L242.4,173.5zM241.7,173.2l-14,-10.2l12.4,-0.1L241.7,173.2zM213.2,161.3l9.1,-5.6l3.9,6.7L213.2,161.3zM204.3,169l-8.4,-7.9l15.7,0.6L204.3,169zM189.3,176.3l-8.8,-1.5l13.8,-13.1L189.3,176.3zM175.7,191.3l-9,-7.4l12.9,-8.1L175.7,191.3zM175.3,191.7l-9.3,3.9l0.5,-11.2L175.3,191.7zM165.8,218.3l-8.3,-13.1l7.9,-8.3L165.8,218.3zM162.3,233.6l-1.6,8l-3.1,-19.7L162.3,233.6zM162.7,234.5l3.9,9.1l-5.6,-0.8L162.7,234.5zM172.9,255.9l-5.9,-1.2l0.4,-9.7L172.9,255.9zM184.1,262.8l-4.2,4l-5.7,-9.7L184.1,262.8zM184.6,263.1l10.1,7.9l-14.3,-3.7L184.6,263.1zM217.6,260.8l14.4,8l-15.8,4.2L217.6,260.8zM247.4,262.3l-0.8,10l-13.1,-3.6L247.4,262.3zM261.8,252.8l-8,14l-5.7,-5L261.8,252.8zM269.8,242.9v7.4l-6.5,1.3L269.8,242.9zM271.5,230.3l6.6,-2.2l-7.5,12.6L271.5,230.3zM276,217.5l3.6,-4.3l-1.2,13.2L276,217.5zM271.9,201.4l3.2,-8l4.3,17.7L271.9,201.4zM264.7,184.4l2.4,-4l7.1,11L264.7,184.4zM264.3,183.9l-7,-9.9l9.2,5.9L264.3,183.9zM241.1,162.8l7.1,-0.8l7.1,9.7L241.1,162.8zM226.9,162.4l-3.8,-6.6l15.7,6.5L226.9,162.4zM212.4,161.1l-3,-5.4l12.2,-0.1L212.4,161.1zM211.7,161.2l-15.3,-0.6l12.3,-4.9L211.7,161.2zM180.9,173.9l5.5,-11.9l8,-0.8L180.9,173.9zM179.8,174.6l-5,-4.8l10.6,-7.3L179.8,174.6zM179.5,175.1l-12.5,8l7.5,-12.8L179.5,175.1zM165.3,195.6l-6.3,-3.3l6.7,-7.7L165.3,195.6zM165.1,196.2l-7.7,8.1l1.4,-11.6L165.1,196.2zM156.5,219l-3.1,2.7l3.4,-13.8L156.5,219zM156.6,219.7l3.2,20.5l-6.8,-17.8L156.6,219.7zM166.7,244.3l-0.4,9.6l-5.2,-10.4L166.7,244.3zM173.2,256.6l5.3,9.1l-11,-10.2L173.2,256.6zM195.7,271.8l-0.2,5.3l-13.9,-9L195.7,271.8zM196.3,271.9l7.3,3.3l-7.6,1.9L196.3,271.9zM215.6,273.7l0.2,7.5l-10.5,-5.5L215.6,273.7zM232.3,269.3l-4.2,10l-11.3,-5.9L232.3,269.3zM233,269.2l13.1,3.5l-17.3,6.5L233,269.2zM248,262.4l5.5,4.8l-6.3,4.9L248,262.4zM262.9,252.1l6.4,-1.3l-13.8,14.4L262.9,252.1zM270.5,249.4v-7.2l5.9,-3.5L270.5,249.4zM271,241.1l7.2,-12l-1.4,8.3L271,241.1zM280,215.3l0.9,8.7l-2,2.7L280,215.3zM275.7,193.2l1.7,1.6l1.7,11.8L275.7,193.2zM267.4,179.9l1.6,-0.8l4.4,10L267.4,179.9zM256.7,172.8l2.7,-3.8l6.6,9.7L256.7,172.8zM256.3,172.4l-7.1,-9.6l9.7,5.8L256.3,172.4zM240.5,162.3v-4.2l6.8,3.4L240.5,162.3zM240,162.2l-15.4,-6.4l15.3,2.1L240,162.2zM209.5,155l2.5,-2.2l8,2.1L209.5,155zM196.2,160l4.7,-5.1l6.6,0.6L196.2,160zM194.9,160.5l-7.3,0.7l11.9,-5.7L194.9,160.5zM175.1,169l0.8,-1.9l6.7,-3.2L175.1,169zM166.2,182.9l-0.5,-4.7l7.3,-6.9L166.2,182.9zM165.7,183.7l-6,6.8l5.5,-11.4L165.7,183.7zM156.9,204.9l-3.8,0.7l5,-11.5L156.9,204.9zM156.7,205.6l-3.6,14.7l-0.2,-13.9L156.7,205.6zM160,242.2l-4.3,-3.5l-2.2,-13.4L160,242.2zM166,254.6l-2.7,-0.9l-1.9,-8.2L166,254.6zM177.9,266.1l-9.1,-5l-1.5,-5L177.9,266.1zM179.6,267.5l3.9,5.1l-12.2,-9.5L179.6,267.5zM181.2,268.5l12.8,8.1l-8.2,-2.7L181.2,268.5zM204.3,275.9l9.8,5.1l-17.2,-3.3L204.3,275.9zM216.2,273.8l11,5.8l-10.8,1.8L216.2,273.8zM245.4,273.5l-4.4,3.8l-10.1,1.6L245.4,273.5zM253.8,267.7l0.4,2.5l-5.8,1.8L253.8,267.7zM268.7,252.3l-6,10.6l-7.7,3.4L268.7,252.3zM270.5,250.5l3.9,-7.4l-4.2,10.1L270.5,250.5zM278.8,227.9l1.6,-2.2l-2.5,8L278.8,227.9zM280.3,212.2V212l0.2,-2l0.2,5.1L280.3,212.2zM279.9,209.2l-1.6,-10.9l1.9,6.2L279.9,209.2zM275.5,192.1l-2.7,-6.3l3.8,7.3L275.5,192.1zM267.1,179.4l-5.5,-8l7.1,7.5L267.1,179.4zM248.8,161.7l0.4,-0.3l4.2,2.9L248.8,161.7zM248.3,161.4l-2.8,-1.5l3.1,1.3L248.3,161.4zM224.1,155.1l5.4,-1.1l8,2.8L224.1,155.1zM222.5,154.9l-7.4,-1.9l12.3,0.8L222.5,154.9zM208.5,155l-5.7,-0.5l8,-1.5L208.5,155zM188.5,160.1l3.2,-2.5l5.2,-1.7L188.5,160.1zM186,161.4l-2.7,1.5l4.3,-2.6L186,161.4zM174.1,169.6l-3.3,3.1l3.8,-4.2L174.1,169.6zM158.5,192l-1.5,0.6l5.8,-9.3L158.5,192zM156.4,193.4l1.7,-0.6l-4,9.2L156.4,193.4zM152.5,217.4l0.1,5.1l-0.3,1L152.5,217.4zM152.8,223.8l1.5,9.4l-2.1,-7.8L152.8,223.8zM160.3,243.3l1.9,8.3l-6,-11.8L160.3,243.3zM166.4,255.4l1.4,4.4l-3.8,-5.2L166.4,255.4zM195.6,277.8l0.6,0.9l-5.7,-2.6L195.6,277.8zM197.1,279.1l-0.7,-1.1l12.5,2.5L197.1,279.1zM227.4,280.2l-1.4,1.5l-6.9,-0.2L227.4,280.2zM228.4,280l8,-1.3l-9.3,2.7L228.4,280zM247,272.9l4.7,-1.5l-8.3,4.6L247,272.9zM254.7,269.9l-0.4,-2.5l7,-3.1L254.7,269.9zM269.7,251.7l-0.3,3.2l-4.5,5.5L269.7,251.7zM277.3,238l1.2,-0.6L273,248L277.3,238zM278.8,236.5l-1.4,0.7l2.7,-8.6L278.8,236.5z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="215.6"
                            android:centerY="220.76"
                            android:gradientRadius="66.13"
                            android:type="radial">
                            <item android:offset="0.91" android:color="#007542E5"/>
                            <item android:offset="1" android:color="#6600DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M339.5,170.7c-5.8,-13.8 -17.6,-29 -26.7,-33.7c6.6,12.8 11.1,26.3 13.4,40.3c0,0 0,0 0,0.1v0.1c10.3,29.4 8.7,61.5 -4.1,89.8c-15.2,32.8 -52.2,66.4 -110,64.6c-62.4,-1.8 -117.6,-48.1 -127.8,-108.9c-1.9,-9.5 0,-14.4 1,-22.2c-1.3,6.1 -2.1,12.2 -2.2,18.4v0.7c0.3,73.6 60.2,133.1 133.7,132.8c64.6,-0.2 119.7,-46.7 130.8,-110.2c0.2,-1.8 0.4,-3.3 0.6,-5.1C350.9,215 347.8,191.9 339.5,170.7L339.5,170.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="311.87"
                            android:centerY="109.01"
                            android:gradientRadius="340.74"
                            android:type="radial">
                            <item android:offset="0" android:color="#FF80EBFF"/>
                            <item android:offset="0.26" android:color="#FF00DDFF"/>
                            <item android:offset="0.53" android:color="#FF0090ED"/>
                            <item android:offset="0.86" android:color="#FF0060DF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M339.5,170.7c-5.8,-13.8 -17.6,-29 -26.7,-33.7c6.6,12.8 11.1,26.3 13.4,40.3c0,0 0,0 0,0.1v0.1c10.3,29.4 8.7,61.5 -4.1,89.8c-15.2,32.8 -52.2,66.4 -110,64.6c-62.4,-1.8 -117.6,-48.1 -127.8,-108.9c-1.9,-9.5 0,-14.4 1,-22.2c-1.3,6.1 -2.1,12.2 -2.2,18.4v0.7c0.3,73.6 60.2,133.1 133.7,132.8c64.6,-0.2 119.7,-46.7 130.8,-110.2c0.2,-1.8 0.4,-3.3 0.6,-5.1C350.9,215 347.8,191.9 339.5,170.7L339.5,170.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="209.99"
                            android:centerY="223.55"
                            android:gradientRadius="340.74"
                            android:type="radial">
                            <item android:offset="0.3" android:color="#CC321C64"/>
                            <item android:offset="0.37" android:color="#7F212F83"/>
                            <item android:offset="0.48" android:color="#230A47AC"/>
                            <item android:offset="0.53" android:color="#000250BB"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M84.2,223.5c10.3,60.7 65.4,107 127.9,108.9c57.8,1.7 94.8,-31.9 110,-64.6c13,-28.3 14.4,-60.5 4.1,-89.8v-0.2c0,-0.1 0,-0.2 0,-0.1v0.2c4.7,30.8 -11,60.7 -35.4,80.9l-0.1,0.2c-47.8,38.9 -93.6,23.5 -102.9,17.2c-0.7,-0.3 -1.3,-0.7 -2,-1c-27.9,-13.3 -39.5,-38.7 -36.9,-60.5c-13.4,0.2 -25.9,-7.7 -31.5,-19.8c14.8,-9.1 33.5,-9.8 49,-2c15.8,7.2 33.8,8 50.1,2c0,-1.1 -23.3,-10.3 -32.3,-19.1c-4.8,-4.7 -7.2,-7.1 -9.1,-8.7c-1.1,-1 -2.3,-1.9 -3.4,-2.7c-0.8,-0.6 -1.7,-1.2 -2.7,-1.9c-9.7,-6.3 -29.1,-6 -29.6,-6h-0.1c-5.3,-6.7 -4.9,-28.8 -4.6,-33.4c-1.6,0.6 -3,1.5 -4.4,2.4c-4.6,3.2 -9,7.1 -13.1,11.2c-4.5,4.6 -8.7,9.6 -12.5,14.9l0,0l0,0c-8.5,12.2 -14.6,25.8 -17.9,40.2C86.5,192 81.8,212.8 84.2,223.5L84.2,223.5z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="308.05"
                            android:centerY="18.36"
                            android:gradientRadius="524.13"
                            android:type="radial">
                            <item android:offset="0" android:color="#FF80EBFF"/>
                            <item android:offset="0.47" android:color="#FF00B3F4"/>
                            <item android:offset="0.84" android:color="#FF0060DF"/>
                            <item android:offset="1" android:color="#FF592ACB"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M263.1,170.8c4.8,4.7 9,10.2 12.4,16.1c0.7,0.5 1.4,1.1 2.1,1.7c30.1,27.9 14.3,67 13.3,69.9c24.5,-20.2 40.2,-50.1 35.4,-80.9c-15,-37.5 -40.5,-52.6 -61.5,-85.6c-1.1,-1.7 -2.2,-3.3 -3.1,-5.1c-0.5,-0.9 -1.1,-1.9 -1.5,-2.7c-0.9,-1.7 -1.6,-3.4 -2.1,-5.3c0,-0.2 -0.1,-0.3 -0.3,-0.4c-0.1,0 -0.2,0 -0.3,0h-0.1h-0.2C252.3,81.1 221.8,129.5 263.1,170.8L263.1,170.8z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="277.55"
                            android:startY="60.69"
                            android:endX="293.77"
                            android:endY="283.77"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFAAF2FF"/>
                            <item android:offset="0.29" android:color="#FF00DDFF"/>
                            <item android:offset="0.61" android:color="#FF0090ED"/>
                            <item android:offset="0.89" android:color="#FF0250BB"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M211.9,152.7l17.7,1.2l10.7,3.8l8.9,3.4l10.3,7.4l9.6,10.2l3.3,6.4c0.8,0.5 1.5,1 2.3,1.5c0.3,0.2 0.6,0.4 0.9,0.6c-3.3,-6 -7.6,-11.4 -12.4,-16.1c-41.4,-41.3 -10.9,-89.7 -5.7,-92.2l0.1,-0.1C224.1,98.3 212.9,134.5 211.9,152.7L211.9,152.7L211.9,152.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="233.01"
                            android:startY="79.68"
                            android:endX="248.96"
                            android:endY="213.99"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFAAF2FF"/>
                            <item android:offset="0.29" android:color="#FF00DDFF"/>
                            <item android:offset="0.74" android:color="#FF0090ED"/>
                            <item android:offset="1" android:color="#FF0250BB"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M168.8,162.3c1,0.6 1.9,1.2 2.7,1.9c-3,-10.6 -3.1,-21.9 -0.4,-32.5c-12.3,6 -23.3,14.3 -32.1,24.7C139.7,156.3 159.1,155.9 168.8,162.3z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="206.55"
                            android:centerY="97.78"
                            android:gradientRadius="97.33"
                            android:type="radial">
                            <item android:offset="0" android:color="#FF00DDFF"/>
                            <item android:offset="0.82" android:color="#FF0090ED"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M156.3,232.3c0,0 6.2,-23 44.1,-23c4.1,0 15.8,-11.4 16,-14.7c-16.3,5.9 -34.3,5.2 -50.1,-2c-15.6,-8 -34.2,-7.2 -49,2c5.7,12.3 18.1,20 31.5,19.8c-2.6,21.8 9,47.1 36.9,60.5c0.6,0.3 1.3,0.6 1.9,0.9C171.3,267.4 158,251.5 156.3,232.3L156.3,232.3z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="270.8"
                            android:centerY="147.43"
                            android:gradientRadius="309.81"
                            android:type="radial">
                            <item android:offset="0.29" android:color="#FF80EBFF"/>
                            <item android:offset="1" android:color="#FF00B3F4"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M277.4,188.7c-0.7,-0.6 -1.4,-1.2 -2.1,-1.7c-0.3,-0.2 -0.6,-0.4 -0.9,-0.6c-9.3,-6.5 -20.8,-9.2 -32.1,-7.9c47.9,24 35,106.3 -31.3,103.3c-5.9,-0.2 -11.8,-1.4 -17.4,-3.3c-1.4,-0.5 -2.7,-1.1 -3.9,-1.7c-0.8,-0.4 -1.6,-0.7 -2.3,-1.1l0.1,0.1c9.2,6.3 55.1,21.8 102.9,-17.2l0.1,-0.2C291.9,255.8 307.8,216.5 277.4,188.7L277.4,188.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:centerX="203.95"
                            android:centerY="132.63"
                            android:gradientRadius="248.83"
                            android:type="radial">
                            <item android:offset="0.18" android:color="#FFAAF2FF"/>
                            <item android:offset="0.43" android:color="#FF00DDFF"/>
                            <item android:offset="0.69" android:color="#FF0060DF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M339.5,170.7c-5.8,-13.8 -17.6,-29 -26.7,-33.7c6.6,12.8 11.1,26.3 13.4,40.3v0.2c-15,-37.5 -40.5,-52.6 -61.5,-85.6c-1.1,-1.7 -2.2,-3.3 -3.1,-5.1c-0.5,-0.9 -1.1,-1.9 -1.5,-2.7c-0.9,-1.7 -1.6,-3.4 -2.1,-5.3c0,-0.2 -0.1,-0.3 -0.3,-0.4c-0.1,0 -0.2,0 -0.3,0c0,0 0,0 -0.1,0c0,0 -0.1,0 -0.1,0.1l0.1,-0.1c-33.5,19.5 -44.9,55.9 -45.8,74h0.3l17.7,1.2l9.9,3.5l9.7,3.7l10.3,7.4l9.6,10.2c0,0 3.3,6.3 3.3,6.4c-8.9,-5.5 -19.5,-7.9 -29.9,-6.4c47.9,24 35,106.3 -31.3,103.3c-5.9,-0.3 -11.8,-1.4 -17.4,-3.3c-1.3,-0.5 -2.7,-1.1 -3.9,-1.7c-0.8,-0.4 -1.6,-0.7 -2.3,-1.1l0.1,0.1c-0.7,-0.3 -1.3,-0.7 -2,-1c0.6,0.3 1.3,0.6 1.9,0.9c-16.3,-8.3 -29.6,-24.2 -31.4,-43.6c0,0 6.2,-23 44.1,-23c4.1,0 15.8,-11.4 16,-14.7c0,-1.1 -23.3,-10.3 -32.3,-19.1c-4.8,-4.7 -7.2,-7.1 -9.1,-8.7c-1.1,-1 -2.3,-1.9 -3.4,-2.7c-3,-10.6 -3.1,-21.9 -0.4,-32.5c-12.3,6 -23.3,14.3 -32.1,24.7h-0.1c-5.3,-6.7 -4.9,-28.8 -4.6,-33.4c-1.6,0.7 -3,1.5 -4.4,2.4c-4.6,3.2 -9,7.1 -13.1,11.2c-4.5,4.6 -8.6,9.6 -12.5,14.9c-8.5,12.2 -14.6,25.8 -17.9,40.2c-0.1,0.3 -0.1,0.6 -0.2,0.9c-0.3,1.2 -1.4,7.2 -1.6,8.3l0,0c-1.1,6.2 -1.7,12.4 -2,18.7v0.7c0.1,73.6 60,133.1 133.5,133c64.8,-0.1 120,-46.7 131,-110.5c0.2,-1.8 0.4,-3.4 0.6,-5.1C350.9,214.9 347.8,191.9 339.5,170.7L339.5,170.7zM326.1,177.6L326.1,177.6L326.1,177.6z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="311.47"
                            android:startY="119.54"
                            android:endX="135.81"
                            android:endY="309.08"
                            android:type="linear">
                            <item android:offset="0.24" android:color="#7F80EBFF"/>
                            <item android:offset="0.7" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M216.4,194.7c0,0 -0.6,0.2 -1.7,0.6c-2.7,4.4 -11.9,12.9 -15.4,12.9c-37.9,-0.2 -44.2,22.5 -44.2,22.5c0.7,7.8 3.2,15.2 7.5,21.8c-3.5,-6.1 -5.7,-13 -6.4,-19.9c0,0 6.2,-23 44.1,-23C204.4,209.4 216.2,197.9 216.4,194.7L216.4,194.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="192.31"
                            android:startY="188.84"
                            android:endX="167.23"
                            android:endY="280.42"
                            android:type="linear">
                            <item android:offset="0" android:color="#E5BFF3FF"/>
                            <item android:offset="1" android:color="#7F80EBFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M326.2,177.7L326.2,177.7c0,-0.2 0,-0.3 0,-0.2C326.2,177.6 326.2,177.5 326.2,177.7c-0.9,-2.3 -1.9,-4.4 -2.7,-6.7c-0.4,-0.8 -0.7,-1.5 -1.1,-2.2c-0.7,-1.4 -1.4,-2.7 -2.1,-4c-0.4,-0.8 -0.9,-1.6 -1.4,-2.4c-0.7,-1.2 -1.3,-2.4 -2,-3.4c-0.5,-0.8 -1,-1.6 -1.5,-2.5c-0.7,-1.1 -1.3,-2.1 -2,-3c-0.5,-0.8 -1.1,-1.6 -1.7,-2.4c-0.7,-1 -1.4,-2 -2.1,-2.7c-0.6,-0.8 -1.2,-1.6 -1.8,-2.4c-0.7,-0.9 -1.4,-1.9 -2.1,-2.7c-0.6,-0.8 -1.2,-1.5 -1.8,-2.3c-0.7,-0.9 -1.4,-1.8 -2.2,-2.7c-0.6,-0.8 -1.3,-1.5 -1.9,-2.3c-0.4,-0.4 -0.7,-0.8 -1,-1.2l0.4,0.5c-11.6,-13.5 -24.1,-26.1 -35.2,-43.9c-1.1,-1.7 -2.2,-3.3 -3.1,-5.1c-0.5,-0.9 -1.1,-1.9 -1.5,-2.7c-0.8,-1.6 -1.4,-3 -1.9,-4.7c0,-0.1 -0.1,-0.2 -0.1,-0.3s-0.1,-0.2 -0.1,-0.3c0,-0.2 -0.1,-0.3 -0.3,-0.4c-0.1,0 -0.2,0 -0.3,0H257c0,0 -0.1,0 -0.1,0.1c-0.3,0.2 -0.6,0.4 -0.8,0.6c-0.1,0.1 -0.1,0.1 -0.2,0.2c-0.4,0.4 -0.8,0.8 -1.1,1.2l0,0c-8,9.3 -28.4,50.2 4.5,86.8c-30.7,-36.1 -11.4,-75.3 -2.9,-85.5c0.5,1.4 1,2.7 1.7,4c0.5,1 1,1.9 1.5,2.7c1.1,1.8 2.2,3.4 3.1,5.1c20.8,33 46.4,48.1 61.5,85.6c0,-0.1 0,-0.2 0,-0.2s0,0 0,0.1v0.1c10.3,29.4 8.7,61.5 -4.1,89.8c-5.3,11.3 -12.5,21.5 -21.2,30.3c9.4,-9.2 17.2,-20 22.8,-32C335.1,239.4 336.4,207.2 326.2,177.7L326.2,177.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="287.95"
                            android:startY="74.23"
                            android:endX="285.22"
                            android:endY="385.68"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFBFF3FF"/>
                            <item android:offset="1" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M257.4,78.8L257.4,78.8c-30,17.6 -42.3,48.7 -45.2,67.9c4.8,-25.1 19.2,-47.4 40.2,-62C253.8,82.3 255.5,80.3 257.4,78.8L257.4,78.8z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="235.68"
                            android:startY="75.96"
                            android:endX="233.41"
                            android:endY="175.93"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFBFF3FF"/>
                            <item android:offset="1" android:color="#7FAAF2FF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M290.7,258.8l0.1,-0.2c1.2,-2.7 17,-42.1 -13.3,-69.9c-0.7,-0.6 -1.4,-1.2 -2.1,-1.7c-0.3,-0.2 -0.6,-0.4 -0.9,-0.6c-9,-6.3 -19.9,-9 -30.9,-8l0,0c-0.4,0.1 -0.8,0.1 -1.3,0.2c47.4,23.8 35.3,104.4 -29.2,103.4h0.5c65.4,2.7 78.2,-77.6 32.9,-102.8c8.9,0.1 17.7,2.7 25.2,7.4c0.3,0.2 0.6,0.4 0.9,0.6c0.7,0.5 1.5,1 2.1,1.6c31.2,26.6 17,66.5 15.9,69.4l-0.1,0.2">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="257.29"
                            android:startY="176.16"
                            android:endX="252.36"
                            android:endY="325.64"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFBFF3FF"/>
                            <item android:offset="1" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M166.7,194c15.3,6.4 32.6,6.9 48.2,1.2c-15.9,5.3 -33.3,4.4 -48.5,-2.6c-15.3,-7.8 -33.6,-7.2 -48.4,1.6l-0.2,0.1l0,0l-0.1,0.1l-0.1,0.1h-0.1l0,0l0,0l0,0l0.1,0.2l0,0l0.1,0.2l0,0l0.1,0.3l0,0c0.3,0.6 0.6,1.2 0.9,1.7c6.1,11 17.9,17.8 30.3,17.6c-2.5,21.1 8.3,45.5 34.6,59.2c-24.9,-14.3 -35.3,-40.4 -32.5,-61.2c-19.2,-0.5 -27.9,-12.1 -30.3,-17C134.9,187.9 152,187.4 166.7,194L166.7,194z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="165.56"
                            android:startY="185.57"
                            android:endX="173.37"
                            android:endY="293.51"
                            android:type="linear">
                            <item android:offset="0" android:color="#CCBFF3FF"/>
                            <item android:offset="1" android:color="#3380EBFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M171.4,164.1l-0.7,-0.4L171.4,164.1L171.4,164.1zM171.3,163.6c-2.8,-10.4 -2.9,-21.5 -0.2,-32c-11.8,5.7 -22.3,13.5 -30.8,23.5c8.1,-8.1 17.7,-14.9 28.2,-19.8C167.2,144.6 168.2,154.4 171.3,163.6z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="176.81"
                            android:startY="124.11"
                            android:endX="147.78"
                            android:endY="173.26"
                            android:type="linear">
                            <item android:offset="0" android:color="#CCBFF3FF"/>
                            <item android:offset="1" android:color="#3380EBFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M87.3,237.4l-0.1,-0.4c-1.3,-4.4 -2.4,-8.9 -3,-13.5c-0.9,-5.7 -0.9,-11.5 0,-17.2c-0.7,4.4 -1.1,8.8 -1.3,13.3v0.7c0,56.6 35.8,107.1 89.4,125.7C126.3,328.1 93.6,286.5 87.3,237.4L87.3,237.4z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="129.41"
                            android:startY="204.87"
                            android:endX="124.93"
                            android:endY="397.63"
                            android:type="linear">
                            <item android:offset="0" android:color="#6600DDFF"/>
                            <item android:offset="1" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M86.2,223.9c-2.4,-10.7 2.5,-31.4 2.6,-31.7c3.2,-14.4 9.3,-28.2 17.9,-40.2l0,0l0,0c3.7,-5.3 8,-10.3 12.5,-14.9c4,-4 8.3,-7.9 13.1,-11.2c0.2,-0.1 0.4,-0.2 0.6,-0.4c0,10.1 0.5,27.2 5.2,32.8h0.1c0.7,0 20,-0.9 29.9,5.1c1.1,0.7 2.1,1.3 2.7,1.8c1.3,0.8 2.5,1.7 3.5,2.6c2.2,1.7 4.4,3.8 9.4,8.4c7.3,6.8 23,13.4 29.6,16.6c-6.7,-3.3 -22.2,-10.4 -29.3,-17.4c-4.8,-4.7 -7.2,-7.1 -9.1,-8.7c-1.1,-1 -2.3,-1.9 -3.4,-2.7c-0.8,-0.6 -1.7,-1.2 -2.7,-1.9c-9.7,-6.3 -29.1,-6 -29.6,-6h-0.1c-4.8,-6.1 -4.9,-25 -4.7,-31.8c0,-0.3 0,-0.5 0,-0.7v-0.2c0,-0.1 0,-0.3 0,-0.4c0,0 0,0 -0.1,0h-0.1h-0.1H134h-0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.2,0.1l-0.1,0.1l-0.2,0.1l-0.1,0.1c-1,0.6 -2.1,1.2 -2.9,1.8c-4.6,3.2 -9,7.1 -13.1,11.2c-4.5,4.6 -8.7,9.6 -12.5,14.9l0,0l0,0C96,163.8 90,177.4 86.7,191.8c-0.1,0.3 -4.8,20.9 -2.6,31.7c8.7,51.6 50,93 100.5,105C135,315.6 95,274.9 86.2,223.9L86.2,223.9z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="148.45"
                            android:startY="98.32"
                            android:endX="148.45"
                            android:endY="326.46"
                            android:type="linear">
                            <item android:offset="0" android:color="#FFBFF3FF"/>
                            <item android:offset="1" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
                <path
                    android:pathData="M339.5,170.7c-5.1,-12.3 -13.2,-23.2 -23.5,-31.6l-0.2,-0.2c-0.3,-0.2 -0.5,-0.4 -0.8,-0.6c-0.1,-0.1 -0.2,-0.1 -0.3,-0.2c-0.3,-0.2 -0.5,-0.3 -0.8,-0.5l-0.3,-0.2c-0.3,-0.2 -0.7,-0.4 -1,-0.6c0.4,0.7 0.7,1.5 1.1,2.2c0.2,0.5 0.4,0.9 0.7,1.4c5.7,11.7 9.6,24.1 11.8,36.9v0.2c4.7,30.8 -11,60.7 -35.4,80.9l-0.1,0.2c-30.4,24.7 -60.2,27.5 -80,24.5c19.8,3.6 51,2.2 83.2,-24l0.1,-0.2c24.5,-20.2 40.2,-50.1 35.4,-80.9v-0.2c-1.9,-11.2 -5.1,-22.1 -9.7,-32.4c5.9,6.2 10.7,13.3 14.5,20.7c9.5,20.3 13.7,42.8 12.4,65.3c-0.1,1.8 -0.2,3.3 -0.3,5.1c-8,63.9 -60.8,112.9 -125,116c-8.7,0.5 -17.7,0.1 -26.3,-1.1c72.1,11.6 140.1,-36.9 152.5,-109c0.2,-1.8 0.4,-3.3 0.6,-5.1C350.9,214.9 347.8,191.9 339.5,170.7L339.5,170.7zM326.2,177.7L326.2,177.7L326.2,177.7z">
                    <aapt:attr name="android:fillColor">
                        <gradient
                            android:startX="276.75"
                            android:startY="130.61"
                            android:endX="267.51"
                            android:endY="444.06"
                            android:type="linear">
                            <item android:offset="0" android:color="#CCBFF3FF"/>
                            <item android:offset="1" android:color="#0000DDFF"/>
                        </gradient>
                    </aapt:attr>
                </path>
            </group>
        </vector>
    </aapt:attr>
    <target
        android:name="a1_t">
        <aapt:attr
            name="android:animation">
            <set>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="300"
                    android:valueFrom="0.75"
                    android:valueTo="0.75"
                    android:valueType="floatType"
                    android:interpolator="@android:interpolator/linear"/>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="700"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="300"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="1000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="2000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="3000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="4000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="5000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="6000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="7000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="8000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="9000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="10000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="11000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="300"
                    android:valueFrom="0.75002400000000002"
                    android:valueTo="0.75002400000000002"
                    android:valueType="floatType"
                    android:interpolator="@android:interpolator/linear"/>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="700"
                    android:valueFrom="0.75002400000000002"
                    android:valueTo="1"
                    android:startOffset="300"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="1000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="2000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="3000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="4000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="5000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="6000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="7000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="8000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="9000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="0.75"
                    android:valueTo="1"
                    android:startOffset="10000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1000"
                    android:valueFrom="1"
                    android:valueTo="0.75"
                    android:startOffset="11000"
                    android:valueType="floatType">
                    <aapt:attr
                        name="android:interpolator">
                        <pathInterpolator
                            android:pathData="M0,0 C0.5,0 0.5,1 1,1"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
</animated-vector>
