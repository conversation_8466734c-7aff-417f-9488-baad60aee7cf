<!DOCTYPE html>
<html>
<meta name="viewport" content="width=device-width">
<body>

<h1>Storage Write</h1>

<p id="cookies"></p>
<button id="setCookies">Set cookies</button>

<script type="text/javascript">
    (function() {
       document.getElementById("cookies").textContent = document.cookie?document.cookie:"No cookies set";
    })();

    document.getElementById("setCookies").addEventListener("click", function() {
        document.cookie = "user=android";
        document.getElementById("cookies").textContent = document.cookie;
    });

    sessionStorage.setItem('sessionTest', 'session storage');
    localStorage.setItem('localTest', 'local storage');

    document.write('<p>Values written to storage</p>');
</script>

</body>
</html>
