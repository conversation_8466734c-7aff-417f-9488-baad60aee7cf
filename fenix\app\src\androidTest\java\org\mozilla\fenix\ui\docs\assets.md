# README
_This document is intended to explain specifics in regards to test assets for the Espresso/UI Automator Android UI tests._

# Test application
* Remote test page managed by Mozilla Mobile Test Engineering at https://github.com/mozilla-mobile/testapp
* File type download page: https://storage.googleapis.com/mobile_test_assets/test_app/downloads.html

# Assets

Currently, Espresso/UI Automator UI tests use remote assets located on a Google Cloud Storage bucket at: [mobile_test_assets](https://storage.googleapis.com/mobile_test_assets)

|Filetype|URL|
|----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|CSV |[CSVfile.csv](https://storage.googleapis.com/mobile_test_assets/public/CSVfile.csv)                                                                                                                                                              |
|DAT |[Data1KB.dat](https://storage.googleapis.com/mobile_test_assets/public/Data1KB.dat)                                                                                                                                                              |
|DOCX|[MyDocument.docx](https://storage.googleapis.com/mobile_test_assets/public/MyDocument.docx)                                                                                                                                                      |
|DOCX|[MyOldWordDocument.doc](https://storage.googleapis.com/mobile_test_assets/public/MyOldWordDocument.doc)                                                                                                                                          |
|EXE |[executable.exe](https://storage.googleapis.com/mobile_test_assets/public/executable.exe)                                                                                                                                                        |
|HTML|[htmlFile.html](https://storage.googleapis.com/mobile_test_assets/public/htmlFile.html)                                                                                                                                                          |
|MP3 |[audioSample.mp3](https://storage.googleapis.com/mobile_test_assets/public/audioSample.mp3)                                                                                                                                                      |
|PDF |[lorem\_upsum.pdf](https://storage.googleapis.com/mobile_test_assets/public/lorem_ipsum.pdf)                                                                                                                                                     |
|PDF |[washington.pdf](https://storage.googleapis.com/mobile_test_assets/public/washington.pdf)                                                                                                                                                        |
|PNG |[web\_icon.png](https://storage.googleapis.com/mobile_test_assets/public/web_icon.png)                                                                                                                                                           |
|SVG |[tAJwqaWjJsXS8AhzSninBMCfIZbHBGgcc001lx5DIdDwIcfEgQ6vE5Gb5VgAled17DFZ2A7ZDOHA0NpQPHXXFt.svg](https://storage.googleapis.com/mobile_test_assets/public/tAJwqaWjJsXS8AhzSninBMCfIZbHBGgcc001lx5DIdDwIcfEgQ6vE5Gb5VgAled17DFZ2A7ZDOHA0NpQPHXXFt.svg)|
|TXT |[textfile.txt](https://storage.googleapis.com/mobile_test_assets/public/textfile.txt)                                                                                                                                                            |
|WEBM|[videoSample.webm](https://storage.googleapis.com/mobile_test_assets/public/videoSample.webm)                                                                                                                                                    |
|XML |[XMLfile.xml](https://storage.googleapis.com/mobile_test_assets/public/XMLfile.xml)                                                                                                                                                              |
|ZIP |[100MB.zip](https://storage.googleapis.com/mobile_test_assets/public/100MB.zip)                                                                                                                                                                  |
|ZIP |[1GB.zip](https://storage.googleapis.com/mobile_test_assets/public/1GB.zip)                                                                                                                                                                      |
|ZIP |[200MB.zip](https://storage.googleapis.com/mobile_test_assets/public/200MB.zip)                                                                                                                                                                  |
|ZIP |[smallZip.zip](https://storage.googleapis.com/mobile_test_assets/public/smallZip.zip)                                                                                                                                                            |

New assets must be approved and uploaded by the [Mozilla Mobile Test Engineering](https://mana.mozilla.org/wiki/display/MTE/Mobile+Test+Engineering) team.
