/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

:root {
  --moz-vertical-spacing: 10px;
  --moz-background-height: 32px;
  /* Default values just to indicate what color variables we use */
  --background-color: #f9f9fb;
  --text-color: #15141a;
  --primary-button-color: #312a65;
  --primary-button-text-color: #ffffff;
  --secondary-button-color: #e0e0e6;
  --secondary-button-text-color: #20123a;
  --header-color: var(--text-color);
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  background-size: 64px var(--moz-background-height);
  background-repeat: repeat-x;
  background-color: var(--background-color);
  color: var(--text-color);
  padding: 0 40px;
  font-size: 14px;
  font-family: sharp-sans, sans-serif;
  -moz-text-size-adjust: none;
}

ul {
  /* Shove the list indicator so that its left aligned, but use outside so that text
    * doesn't don't wrap the text around it */
  padding: 0 1em;
  margin: 0;
  list-style-type: disc;
}

#errorShortDesc,
li:not(:last-of-type) {
  /* Margins between the li and buttons below it won't be collapsed. Remove the bottom margin here. */
  margin: var(--moz-vertical-spacing) 0;
}

h1 {
  margin: 0;
  padding: 0;
  margin: var(--moz-vertical-spacing) 0;
  color: var(--header-color);
  font-weight: bold;
  font-size: 20px;
  line-height: 24px;
}

p {
  line-height: 20px;
  margin: var(--moz-vertical-spacing) 0;
}

button {
  display: block;
  height: 36px;
  box-sizing: content-box;
  width: 100%;
  border: 0;
  padding: 6px 0px;
  font-family: inherit;
  background-color: transparent;
  color: var(--primary-button-text-color);
  font-size: 14px;
  font-weight: bold;
  margin: 0 auto;
  text-align: center;
  position: relative;
}

button::after {
  background-color: var(--primary-button-color);
  content: '';
  border-radius: 5px;
  display: block;
  position: absolute;
  top: 6px;
  left: 0px;
  right: 0px;
  bottom: 6px;
  z-index: -1;
}

hr {
  height: 1px;
  border: 0;
  background: rgba(21, 20, 26, 0.12);
  margin: 32px 0;
}

.horizontalLine {
  margin-left: -40px;
  margin-right: -40px;
}

.buttonSecondary {
  background-color: transparent;
  color: var(--secondary-button-text-color);
}

.buttonSecondary::after {
  background-color: var(--secondary-button-color);
}

#errorPageContainer {
  /* If the page is greater than 550px center the content.
    * This number should be kept in sync with the media query for tablets below */
  max-width: 550px;
  margin: 0 auto;
  min-height: 100%;
}

/* On large width devices, apply specific styles here. Often triggered by landscape mode or tablets */
@media (min-width: 550px) {
  button,
  .buttonSecondary {
    margin: var(--moz-vertical-spacing) auto;
    min-width: 400px;
    width: auto;
  }

  /* If the device is tall as well, add some padding to make content feel a bit more centered */
  @media (min-height: 550px) {
    #errorPageContainer {
      padding-top: 64px;
      min-height: calc(100% - 64px);
    }
  }
}

#badCertTechnicalInfo {
  overflow: auto;
  white-space: pre-line;
}

#advancedPanelButtonContainer {
  display: flex;
  justify-content: center;
}
